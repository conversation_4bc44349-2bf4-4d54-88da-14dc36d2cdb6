package main

import (
	"fmt"
	"log"
	"os"
	"time"
)

// 获取日志文件路径
func getLogFilePath() string {
	// 尝试获取当前工作目录
	if wd, err := os.Getwd(); err == nil {
		return wd + "/friend_relation_debug.log"
	}
	// 如果获取失败，使用相对路径
	return "friend_relation_debug.log"
}

// 创建日志文件
func createLogFile() *os.File {
	logPath := getLogFilePath()
	logFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("无法创建日志文件 %s: %v", logPath, err)
		return nil
	}
	return logFile
}

// 写入日志
func writeLog(message string) {
	// 强制输出到控制台
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logMessage := fmt.Sprintf("[%s] [好友关系检测] %s", timestamp, message)
	
	// 输出到控制台
	log.Println(logMessage)
	fmt.Println(logMessage) // 双重保险
	
	// 尝试写入文件
	logFile := createLogFile()
	if logFile != nil {
		defer logFile.Close()
		logFile.WriteString(logMessage + "\n")
		logFile.Sync() // 强制刷新到磁盘
	}
}

func main() {
	fmt.Println("测试日志功能...")
	
	// 显示当前工作目录
	if wd, err := os.Getwd(); err == nil {
		fmt.Printf("当前工作目录: %s\n", wd)
	}
	
	// 显示日志文件路径
	fmt.Printf("日志文件路径: %s\n", getLogFilePath())
	
	// 测试写入日志
	writeLog("这是一条测试日志")
	writeLog("日志功能测试完成")
	
	fmt.Println("测试完成，请检查日志文件和控制台输出")
}
