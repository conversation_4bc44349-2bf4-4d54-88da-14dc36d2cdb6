package controllers

import (
	"encoding/json"
	"fmt"
	"github.com/astaxie/beego"
	"strings"
	"time"
	"wechatdll/lib"
	"wechatdll/models"
	"wechatdll/models/Login"
)

// 登陆模块 支持二次 唤醒 62数据登陆(注意：代理必须使用SOCKS)
type LoginController struct {
	beego.Controller
}

// @Summary 获取二维码
// @Param	body		body 	Login.GetQRReq	true		"不使用代理请留空"
// @Success 200
// @router /GetQR [post]
func (c *LoginController) LoginGetQR() {
	var GetQR Login.GetQRReq
	data := c.Ctx.Input.RequestBody
	fmt.Println("请求明文：", data)
	err := json.Unmarshal(data, &GetQR)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	//生成设备ID
	if GetQR.DeviceID == "" || GetQR.DeviceID == "string" {
		GetQR.DeviceID = lib.CreateDeviceId(GetQR.DeviceID)
	}

	if GetQR.DeviceName == "" || GetQR.DeviceName == "string" {
		GetQR.DeviceName = "iPad"
		//GetQR.DeviceName = "DESKTOP-" + lib.RandSeq(7)
	}
	//if GetQR.OSModel == "" || GetQR.OSModel == "string" {
	//	GetQR.OSModel = "windows"
	//}
	begin := time.Now()
	WXDATA := Login.GetQRCODE(GetQR)
	fmt.Println("获取二维码的时间：", time.Now().Sub(begin))
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 检测二维码
// @Param	uuid		query 	string	true		"请输入取码时返回的UUID"
// @Success 200
// @router /CheckQR [post]
func (c *LoginController) LoginCheckQR() {
	uuid := c.GetString("uuid")
	begin := time.Now()
	WXDATA := Login.CheckUuid(uuid)
	fmt.Println("获取二维码的时间：", time.Now().Sub(begin))
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

func Between(str, starting, ending string) string {
	s := strings.Index(str, starting)
	if s < 0 {
		return ""
	}

	s += len(starting)
	e := strings.Index(str[s:], ending)
	if e < 0 {
		return ""
	}
	return str[s : s+e]
}

// @Summary 二次登陆
// @Param	body			body 	Login.OsParam	true		"二次登陆"
// @Success 200
// @router /TwiceAutoAuth [post]
func (c *LoginController) LoginTwiceAutoAuth() {
	fmt.Println("=============================================================")
	fmt.Println("开始了啊")
	var reqdata Login.OsParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.Secautoauth(reqdata)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary DeviceToken更新
// @Param	body			body 	Login.UpdateDeviceTokenParam	true		"DeviceToken更新"
// @Success 200
// @router /UpdateDeviceToken [post]
func (c *LoginController) UpdateDeviceToken() {
	var reqdata Login.UpdateDeviceTokenParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.UpdateDeviceToken(reqdata)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 62登陆(账号或密码)
// @Param	body			body 	Login.Data62LoginReq	true		"不使用代理请留空"
// @Failure 200
// @router /62data [post]
func (c *LoginController) Data62Login() {
	var reqdata Login.Data62LoginReq
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Login.Data62(reqdata, "extshort.weixin.qq.com")
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary A16登陆(账号或密码) - android == 7.0.14
// @Param	body			body 	Login.A16LoginParam	true		"不使用代理请留空"
// @Failure 200
// @router /A16Data [post]
func (c *LoginController) A16Data() {
	var reqdata Login.A16LoginParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Login.AndroidA16Login(reqdata, "extshort.weixin.qq.com")
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 心跳包
// @Param	wxid			query 	string	true		"请输入登陆成功的wxid"
// @Success 200
// @router /HeartBeat [post]
func (c *LoginController) HeartBeat() {
	wxid := c.GetString("wxid")
	WXDATA := Login.HeartBeat(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 初始化
// @Param	wxid			query 	string	true		"请输入登陆成功的wxid"
// @Success 200
// @router /Newinit [post]
func (c *LoginController) Newinit() {
	wxid := c.GetString("wxid")
	WXDATA := Login.Newinit(wxid, "", "")
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 唤醒登陆(只限扫码登录)
// @Param	body			body 	Login.OsParam	true		"唤醒登陆"
// @Success 200
// @router /Awaken [post]
func (c *LoginController) LoginAwaken() {
	var reqdata Login.OsParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.AwakenLogin(reqdata)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取登陆缓存信息
// @Param	wxid		query 	string	true		"请输入登陆成功的wxid"
// @Success 200
// @router /GetCacheInfo [post]
func (c *LoginController) GetCacheInfo() {
	wxid := c.GetString("wxid")
	WXDATA := Login.CacheInfo(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 获取62数据
// @Param	wxid		query 	string	true		"请输入登陆成功的wxid"
// @Success 200
// @router /Get62Data [post]
func (c *LoginController) Get62Data() {
	wxid := c.GetString("wxid")
	Data62 := Login.Get62Data(wxid)
	Result := models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Data62,
	}
	c.Data["json"] = &Result
	c.ServeJSON()
	return
}

// @Summary 退出登录
// @Param	wxid			query 	string	true		"请输入登陆成功的wxid"
// @Success 200
// @router /LogOut [post]
func (c *LoginController) LogOut() {
	wxid := c.GetString("wxid")
	WXDATA := Login.LogOut(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 新设备扫码登录
// @Param	body			body 	Login.ExtDeviceLoginConfirmParam	true		"URL == MAC iPad Windows 的微信二维码解析出来的url"
// @Success 200
// @router /ExtDeviceLoginConfirmGet [post]
func (c *LoginController) ExtDeviceLoginConfirmGet() {
	var reqdata Login.ExtDeviceLoginConfirmParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.ExtDeviceLoginConfirmGet(reqdata)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 新设备扫码确认登录
// @Param	body			body 	Login.ExtDeviceLoginConfirmParam	true		"URL == MAC iPad Windows 的微信二维码解析出来的url"
// @Success 200
// @router /ExtDeviceLoginConfirmOk [post]
func (c *LoginController) ExtDeviceLoginConfirmOk() {
	var reqdata Login.ExtDeviceLoginConfirmParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.ExtDeviceLoginConfirmOk(reqdata)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 新服务一键推送登录
// @Param	body			body 	Login.Rouses	true		"新服务一键推送登录"
// @Success 200
// @router /OneClickLoginToNewService [post]
func (c *LoginController) OneClickLoginToNewService() {
	var reqdata Login.Rouses
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)

	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}

	WXDATA := Login.OneClickLoginToNewService(reqdata.Wxid, reqdata.Url, reqdata.OS, reqdata.Proxy)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 铺助手机扫码登录
// @Param	body			body 	Login.PhoneDeviceLoginParam	true		"URL == 手机微信二维码解析出来的url"
// @Success 200
// @router /PhoneDeviceLogin [post]
func (c *LoginController) PhoneDeviceLogin() {
	var reqdata Login.PhoneDeviceLoginParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &reqdata)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Login.PhoneDeviceLogin(reqdata)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}
