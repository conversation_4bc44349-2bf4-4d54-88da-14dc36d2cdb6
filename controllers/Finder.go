package controllers

import (
	"encoding/json"
	"fmt"
	"github.com/astaxie/beego"
	"wechatdll/models"
	"wechatdll/models/Finder"
)

// 视频号模块
type FinderController struct {
	beego.Controller
}

// @Summary 用户中心
// @Param	wxid		query 	string	true		"请输登陆后的wxid"
// @Success 200
// @router /UserPrepare [post]
func (c *FinderController) UserPrepare() {
	wxid := c.GetString("wxid")
	WXDATA := Finder.UserPrepare(wxid)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 评论
// @Param	body		body 	Finder.CommentParam   true	"评论"
// @Success 200
// @router /Comment [post]
func (c *FinderController) Comment() {
	var ParamData Finder.CommentParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.Comment(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 关注
// @Param	body		body 	Finder.DefaultParam   true	"关注"
// @Success 200
// @router /Follow [post]
func (c *FinderController) Follow() {
	var ParamData Finder.DefaultParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.Follow(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 查看指定内容
// @Param	body		body 	Finder.GetCommentDetailParam   true	"查看指定内容"
// @Success 200
// @router /GetCommentDetail [post]
func (c *FinderController) GetCommentDetail() {
	var ParamData Finder.GetCommentDetailParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.GetCommentDetail(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 点赞
// @Param	body		body 	Finder.LikeParam   true	"点赞"
// @Success 200
// @router /Like [post]
func (c *FinderController) Like() {
	var ParamData Finder.LikeParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.Like(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 用户搜索
// @Param	body		body 	Finder.DefaultParam   true	"用户搜索"
// @Success 200
// @router /Search [post]
func (c *FinderController) Search() {
	var ParamData Finder.DefaultParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.Search(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 查看指定人首页
// @Param	body		body 	Finder.TargetUserPageParam   true	"查看指定人首页"
// @Success 200
// @router /TargetUserPage [post]
func (c *FinderController) TargetUserPage() {
	var ParamData Finder.TargetUserPageParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.TargetUserPage(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 推荐
// @Param	body		body 	Finder.GetRecommendParam   true	"推荐首页"
// @Success 200
// @router /GetRecommend [post]
func (c *FinderController) GetRecommend() {
	var ParamData Finder.GetRecommendParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.FinderStream(ParamData) // Finder.GetRecommend(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 搜索列表
// @Param	body		body 	Finder.FinderSearchParam   true	"搜索列表"
// @Success 200
// @router /FinderSearchList [post]
func (c *FinderController) FinderSearchList() {
	var ParamData Finder.FinderSearchParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.FinderSearchList(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 主题列表
// @Param	body		body 	Finder.FinderGetTopicListParam   true	"主题列表"
// @Success 200
// @router /Findergettopiclist [post]
func (c *FinderController) Findergettopiclist() {
	var ParamData Finder.FinderGetTopicListParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.FinderGetTopicList(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 直播详情
// @Param	body		body 	Finder.FinderLiveDetailParam   true	"直播详情"
// @Success 200
// @router /FinderLiveDetail [post]
func (c *FinderController) FinderLiveDetail() {
	var ParamData Finder.FinderLiveDetailParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.FinderGetCommentDetail(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}

// @Summary 发送私信文字
// @Param	body		body 	Finder.FinderSendTextParam   true	"直播详情"
// @Success 200
// @router /FinderSendText [post]
func (c *FinderController) FinderSendText() {
	var ParamData Finder.FinderSendTextParam
	data := c.Ctx.Input.RequestBody
	err := json.Unmarshal(data, &ParamData)
	if err != nil {
		Result := models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
		c.Data["json"] = &Result
		c.ServeJSON()
		return
	}
	WXDATA := Finder.FinderSendText(ParamData)
	c.Data["json"] = &WXDATA
	c.ServeJSON()
}
