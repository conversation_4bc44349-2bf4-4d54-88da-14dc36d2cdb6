// GENERATED BY THE COMMAND ABOVE; DO NOT EDIT
// This file was generated by swaggo/swag

package docs

import (
	"bytes"
	"encoding/json"
	"strings"

	"github.com/alecthomas/template"
	"github.com/swaggo/swag"
)

var doc = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{.Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/62data": {
            "post": {
                "summary": "62登陆(账号或密码)",
                "parameters": [
                    {
                        "description": "不使用代理请留空",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Login.Data62LoginReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/A16Data": {
            "post": {
                "summary": "A16登陆(账号或密码) - android == 7.0.14",
                "parameters": [
                    {
                        "description": "不使用代理请留空",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Login.A16LoginParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Add": {
            "post": {
                "summary": "添加标签",
                "parameters": [
                    {
                        "description": "添加标签",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Label.AddParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/AddChatRoomMember": {
            "post": {
                "summary": "增加群成员(40人以内)",
                "parameters": [
                    {
                        "description": "ToWxids 多个微信ID用,隔开 ChatRoomName 群ID",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.AddChatRoomParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Awaken": {
            "post": {
                "summary": "唤醒登陆(只限扫码登录)",
                "parameters": [
                    {
                        "description": "唤醒登陆",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Login.OsParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/BatchSendMsg": {
            "post": {
                "summary": "批量发送文本消息",
                "parameters": [
                    {
                        "description": "ToWxids用,隔开",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.BatchSendMsgParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/BindingEmail": {
            "post": {
                "summary": "绑定邮箱",
                "parameters": [
                    {
                        "description": "绑定邮箱",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.EmailParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/BindingMobile": {
            "post": {
                "summary": "换绑手机号",
                "parameters": [
                    {
                        "description": "Mobile == 格式：+8617399999999 Verifycode == 验证码请先通过(发送手机验证码)获取",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.BindMobileParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Blacklist": {
            "post": {
                "summary": "添加/移除黑名单",
                "parameters": [
                    {
                        "description": "Val == 15添加  7移除",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Friend.BlacklistParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/CheckCanSetAlias": {
            "post": {
                "summary": "检查微信号安全环境",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登陆后的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/CheckQR": {
            "post": {
                "summary": "检测二维码",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入取码时返回的UUID",
                        "name": "uuid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Comment": {
            "post": {
                "summary": "朋友圈点赞/评论",
                "parameters": [
                    {
                        "description": "type：1点赞 2：文本 3:消息 4：with 5陌生人点赞 replyCommnetId：回复评论Id",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FriendCircle.CommentParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ConsentToJoin": {
            "post": {
                "summary": "同意进入群聊",
                "parameters": [
                    {
                        "description": "Url请在消息内容xml中查找",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.ConsentToJoinParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/CreateChatRoom": {
            "post": {
                "summary": "创建群聊",
                "parameters": [
                    {
                        "description": "ToWxids 多个微信ID用,隔开 至少三个好友微信ID以上",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.CreateChatRoomParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Del": {
            "post": {
                "summary": "删除收藏",
                "parameters": [
                    {
                        "description": "FavId在同步收藏中获取",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Favor.DelParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/DelChatRoomMember": {
            "post": {
                "summary": "删除群成员",
                "parameters": [
                    {
                        "description": "ToWxids 多个微信ID用,隔开 ChatRoomName 群ID",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.AddChatRoomParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/DelSafetyInfo": {
            "post": {
                "summary": "删除登录设备",
                "parameters": [
                    {
                        "description": "UUID请在登录设备管理中获取",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.DelSafetyInfoParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Delete": {
            "post": {
                "summary": "删除标签",
                "parameters": [
                    {
                        "description": "删除标签",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Label.DeleteParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/DownloadFile": {
            "post": {
                "summary": "文件下载",
                "parameters": [
                    {
                        "description": "DataLen == 文件大小, xml中获取",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Tools.DownloadAppAttachParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/DownloadImg": {
            "post": {
                "summary": "高清图片下载",
                "parameters": [
                    {
                        "description": "DataLen == 图片大小, xml中获取",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Tools.DownloadParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/DownloadVideo": {
            "post": {
                "summary": "视频下载",
                "parameters": [
                    {
                        "description": "DataLen == 视频大小, xml中获取",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Tools.DownloadParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/DownloadVoice": {
            "post": {
                "summary": "语音下载",
                "parameters": [
                    {
                        "description": "注意参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Tools.DownloadVoiceParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/EmojiDownload": {
            "post": {
                "summary": "Emoji下载",
                "parameters": [
                    {
                        "description": "md5就是动图表情的md5,在xml中查找",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Tools.DownLoadEmojiParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ExtDeviceLoginConfirmGet": {
            "post": {
                "summary": "新设备扫码登录",
                "parameters": [
                    {
                        "description": "URL == MAC iPad Windows 的微信二维码解析出来的url",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Login.ExtDeviceLoginConfirmParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ExtDeviceLoginConfirmOk": {
            "post": {
                "summary": "新设备扫码确认登录",
                "parameters": [
                    {
                        "description": "URL == MAC iPad Windows 的微信二维码解析出来的url",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Login.ExtDeviceLoginConfirmParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/FinderLiveDetail": {
            "post": {
                "summary": "直播详情",
                "parameters": [
                    {
                        "description": "直播详情",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Finder.FinderLiveDetailParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/FinderSearchList": {
            "post": {
                "summary": "搜索列表",
                "parameters": [
                    {
                        "description": "搜索列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Finder.FinderSearchParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/FinderSendText": {
            "post": {
                "summary": "发送私信文字",
                "parameters": [
                    {
                        "description": "直播详情",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Finder.FinderSendTextParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Findergettopiclist": {
            "post": {
                "summary": "主题列表",
                "parameters": [
                    {
                        "description": "主题列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Finder.FinderGetTopicListParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Follow": {
            "post": {
                "summary": "关注",
                "parameters": [
                    {
                        "description": "关注",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/OfficialAccounts.DefaultParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GeneratePayQCode": {
            "get": {
                "summary": "生成支付二维码",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登录后的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Get62Data": {
            "post": {
                "summary": "获取62数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登陆成功的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetA8Key": {
            "post": {
                "summary": "GetA8Key",
                "parameters": [
                    {
                        "description": "OpCode == 2 Scene == 4 CodeType == 19 CodeVersion == 5 以上是默认参数,如有需求自行修改",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Tools.GetA8KeyParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetAppMsgExt": {
            "post": {
                "summary": "阅读文章,返回 分享、看一看、阅读数据",
                "parameters": [
                    {
                        "description": "阅读文章",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/OfficialAccounts.ReadParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetBandCardList": {
            "post": {
                "summary": "获取余额以及银行卡信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登录后的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetBoundHardDevices": {
            "post": {
                "summary": "GetBoundHardDevices",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登录后的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetCacheInfo": {
            "post": {
                "summary": "获取登陆缓存信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登陆成功的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetChatRoomInfo": {
            "post": {
                "summary": "获取群详情(不带公告内容)",
                "parameters": [
                    {
                        "description": "UserNameList == 群ID,多个查询请用,隔开",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.GetChatRoomParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetChatRoomInfoDetail": {
            "post": {
                "summary": "获取群信息(带公告内容)",
                "parameters": [
                    {
                        "description": "QID == 群ID",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.GetChatRoomParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetChatRoomMemberDetail": {
            "post": {
                "summary": "获取群成员",
                "parameters": [
                    {
                        "description": "QID == 群ID",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.GetChatRoomParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetCommentDetail": {
            "post": {
                "summary": "查看指定内容",
                "parameters": [
                    {
                        "description": "查看指定内容",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Finder.GetCommentDetailParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetContractDetail": {
            "post": {
                "summary": "获取通讯录好友详情(20上限)",
                "parameters": [
                    {
                        "description": "多个微信请用,隔开(最多20个),ChatRoom请留空",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Friend.GetContractDetailparameter"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetContractDetail100": {
            "post": {
                "summary": "获取通讯录好友详情(100上限)",
                "parameters": [
                    {
                        "description": "多个微信请用,隔开(最多100个)",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Friend.DefaultParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetContractList": {
            "post": {
                "summary": "获取通讯录好友",
                "parameters": [
                    {
                        "description": "CurrentWxcontactSeq和CurrentChatRoomContactSeq没有的情况下请填写0",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Friend.GetContractListparameter"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetContractProfile": {
            "post": {
                "summary": "取个人信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登陆后的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetDetail": {
            "post": {
                "summary": "获取特定人朋友圈",
                "parameters": [
                    {
                        "description": "打开首页时：Fristpagemd5留空,Maxid填写0",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FriendCircle.GetDetailparameter"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetFavInfo": {
            "post": {
                "summary": "获取搜藏信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登陆后的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetFavItem": {
            "post": {
                "summary": "读取收藏内容",
                "parameters": [
                    {
                        "description": "FavId在同步收藏中获取",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Favor.GetFavItemParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetIdDetail": {
            "post": {
                "summary": "获取特定ID详情内容",
                "parameters": [
                    {
                        "description": "Id为当前朋友圈内容的id",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FriendCircle.GetIdDetailParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetList": {
            "post": {
                "summary": "朋友圈首页列表",
                "parameters": [
                    {
                        "description": "打开首页时：Fristpagemd5留空,Maxid填写0",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FriendCircle.GetListParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetMFriend": {
            "post": {
                "summary": "获取手机通讯录",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登陆后的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetQR": {
            "post": {
                "summary": "获取二维码",
                "parameters": [
                    {
                        "description": "不使用代理请留空",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Login.GetQRReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetQRCode": {
            "post": {
                "summary": "取个人二维码",
                "parameters": [
                    {
                        "description": "Style == 二维码样式(请自行探索) 8默认",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.GetQRCodeParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetRealNameInfo": {
            "get": {
                "summary": "获取实名信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登录后的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetRecommend": {
            "post": {
                "summary": "推荐",
                "parameters": [
                    {
                        "description": "推荐首页",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Finder.GetRecommendParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetSafetyInfo": {
            "post": {
                "summary": "登录设备管理",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登陆后的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetSomeMemberInfo": {
            "post": {
                "summary": "获取群成员信息",
                "parameters": [
                    {
                        "description": "多个ToWxid请用,隔开。",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.QidToWxidParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GetWxidList": {
            "post": {
                "summary": "读取标签列表",
                "parameters": [
                    {
                        "description": "读取标签列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Label.DeleteParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/GroupListApi": {
            "post": {
                "summary": "同步群",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.GroupListParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/HeartBeat": {
            "post": {
                "summary": "心跳包",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登陆成功的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/InviteChatRoomMember": {
            "post": {
                "summary": "邀请群成员(40人以上)",
                "parameters": [
                    {
                        "description": "ToWxids 多个微信ID用,隔开 ChatRoomName 群ID",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.AddChatRoomParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/JSAPIPreVerify": {
            "post": {
                "summary": "JSAPIPreVerify",
                "parameters": [
                    {
                        "description": "JSAPIPreVerify",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/OfficialAccounts.GetkeyParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/JSLogin": {
            "post": {
                "summary": "授权小程序(返回授权后的code)",
                "parameters": [
                    {
                        "description": "授权小程序",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Wxapp.DefaultParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/JSOperateWxData": {
            "post": {
                "summary": "小程序操作",
                "parameters": [
                    {
                        "description": "小程序操作",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Wxapp.JSOperateWxParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Lbs": {
            "post": {
                "summary": "附近人",
                "parameters": [
                    {
                        "description": "Longitude == 经度，Latitude == 维度 OpCode == 没有特殊情况请填1",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Friend.LbsFindParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Like": {
            "post": {
                "summary": "点赞",
                "parameters": [
                    {
                        "description": "点赞",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Finder.LikeParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/LogOut": {
            "post": {
                "summary": "退出登录",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登陆成功的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Messages": {
            "post": {
                "summary": "发布朋友圈",
                "parameters": [
                    {
                        "description": "请自行构造xml内容,注意:如果发的不是视频ISVideo记得提交false",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FriendCircle.Messagearameter"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/MmSnsSync": {
            "post": {
                "summary": "朋友圈同步",
                "parameters": [
                    {
                        "description": "Synckey可留空",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FriendCircle.MmSnsSyncParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Modelv1": {
            "post": {
                "summary": "模式-扫码",
                "parameters": [
                    {
                        "description": "注意,请先执行1再执行2",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/SayHello.Model1Param"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Modelv2": {
            "post": {
                "summary": "模式-一键打招呼",
                "parameters": [
                    {
                        "description": "Scene 招呼通道 FromScene SearchScene 搜索联系人场景",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/SayHello.Model2Param"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/MoveContractList": {
            "post": {
                "summary": "保存到通讯录",
                "parameters": [
                    {
                        "description": "Val == 3添加 2移除",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.MoveContractListParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/MpGetA8Key": {
            "post": {
                "summary": "MpGetA8Key(获取文章key和uin)",
                "parameters": [
                    {
                        "description": "获取文章key和uin",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/OfficialAccounts.ReadParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Newinit": {
            "post": {
                "summary": "初始化",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输入登陆成功的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/OauthAuthorize": {
            "post": {
                "summary": "OauthAuthorize",
                "parameters": [
                    {
                        "description": "OauthAuthorize",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/OfficialAccounts.GetkeyParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/OneClickLoginToNewService": {
            "post": {
                "summary": "新服务一键推送登录",
                "parameters": [
                    {
                        "description": "新服务一键推送登录",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Login.Rouses"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/OnekeySetPasswd": {
            "post": {
                "summary": "一键修改密码",
                "parameters": [
                    {
                        "description": "一键修改密码",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.OnekeySetPasswdParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/OperateChatRoomAdmin": {
            "post": {
                "summary": "群管理操作(添加、删除、转让)",
                "parameters": [
                    {
                        "description": "ToWxids == 多个wxid用,隔开(仅限于添加/删除管理员) Val == 1添加 2删除 3转让",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.OperateChatRoomAdminParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Operation": {
            "post": {
                "summary": "朋友圈操作",
                "parameters": [
                    {
                        "description": "type：1删除朋友圈2设为隐私3设为公开4删除评论5取消点赞",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FriendCircle.OperationParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/PassVerify": {
            "post": {
                "summary": "通过好友请求",
                "parameters": [
                    {
                        "description": "Scene：代表来源,请在消息中的xml中获取",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Friend.PassVerifyParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Pat": {
            "post": {
                "summary": "拍一拍群人员(爱她就拍她,类似@)",
                "parameters": [
                    {
                        "description": "QID == 群ID, ToWxid == 不支持多个",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.QidToWxidParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/PhoneDeviceLogin": {
            "post": {
                "summary": "铺助手机扫码登录",
                "parameters": [
                    {
                        "description": "URL == 手机微信二维码解析出来的url",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Login.PhoneDeviceLoginParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/PrivacySettings": {
            "post": {
                "summary": "朋友圈权限设置",
                "parameters": [
                    {
                        "description": "核心参数请联系客服获取代码列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FriendCircle.PrivacySettingsParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Quit": {
            "post": {
                "summary": "取消关注",
                "parameters": [
                    {
                        "description": "取消关注",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/OfficialAccounts.DefaultParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/RemindMsg": {
            "post": {
                "summary": "发送提醒消息",
                "parameters": [
                    {
                        "description": "Ytitle==提醒名 比如(官方提醒) Ycontent == 提醒内容 比如(楼上这位是本群的监督员)",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.RemindMsgParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ReportMotion": {
            "post": {
                "summary": "ReportMotion",
                "parameters": [
                    {
                        "description": "具体用法请联系客服",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.ReportMotionParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ReviseAlisa": {
            "post": {
                "summary": "修改微信号(一年一次)",
                "parameters": [
                    {
                        "description": "SafeTicket == 检查微信号安全环境返回的Ticket  PwdTicket == 验证密码返回的Ticket",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.ReviseAlisa"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Revoke": {
            "post": {
                "summary": "撤回消息",
                "parameters": [
                    {
                        "description": "请注意参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.RevokeMsgParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ScanIntoGroup": {
            "post": {
                "summary": "扫码进群",
                "parameters": [
                    {
                        "description": "只支持url",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.ScanIntoGroupParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Search": {
            "post": {
                "summary": "用户搜索",
                "parameters": [
                    {
                        "description": "用户搜索",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Finder.DefaultParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendApp": {
            "post": {
                "summary": "发送App消息",
                "parameters": [
                    {
                        "description": "Type请根据场景设置,xml请自行构造",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.SendAppMsgParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendCDNFile": {
            "post": {
                "summary": "发送文件(转发,并非上传)",
                "parameters": [
                    {
                        "description": "Content==收到文件消息xml",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.DefaultParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendCDNImg": {
            "post": {
                "summary": "发送Cdn图片(转发图片)",
                "parameters": [
                    {
                        "description": "Content==消息xml",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.DefaultParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendCDNVideo": {
            "post": {
                "summary": "发送Cdn视频(转发视频)",
                "parameters": [
                    {
                        "description": "Content==消息xml",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.DefaultParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendEmoji": {
            "post": {
                "summary": "发送Emoji",
                "parameters": [
                    {
                        "description": "发送Emoji",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.SendEmojiParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendRequest": {
            "post": {
                "summary": "添加联系人(发送好友请求)",
                "parameters": [
                    {
                        "description": "V1 V2是必填项",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Friend.SendRequestParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendTransmitMsg": {
            "post": {
                "summary": "转发聊天记录",
                "parameters": [
                    {
                        "description": "xml请自行构造",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.SendTransmitParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendTxt": {
            "post": {
                "summary": "发送文本消息",
                "parameters": [
                    {
                        "description": "Type请填写1 At == 群@,多个wxid请用,隔开",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.SendNewMsgParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendVerifyMobile": {
            "post": {
                "summary": "发送手机验证码",
                "parameters": [
                    {
                        "description": "Opcode == 场景(18代表绑手机号) Mobile == 格式：+8617399999999",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.SendVerifyMobileParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendVideo": {
            "post": {
                "summary": "发送视频",
                "parameters": [
                    {
                        "description": "发送视频",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.SendVideoMsgParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SendVoice": {
            "post": {
                "summary": "发送语音",
                "parameters": [
                    {
                        "description": "Type： AMR = 0, MP3 = 2, SILK = 4, SPEEX = 1, WAVE = 3 VoiceTime ：音频长度 1000为一秒",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.SendVoiceMessageParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SetAlisa": {
            "post": {
                "summary": "设置微信号",
                "parameters": [
                    {
                        "description": "设置微信号",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.SetAlisaParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SetChatRoomAnnouncement": {
            "post": {
                "summary": "设置群公告",
                "parameters": [
                    {
                        "description": "Content == 公告内容",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.OperateChatRoomInfoParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SetChatRoomName": {
            "post": {
                "summary": "设置群名称",
                "parameters": [
                    {
                        "description": "Content == 名称",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.OperateChatRoomInfoParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SetChatRoomRemarks": {
            "post": {
                "summary": "设置群备注(仅自己可见)",
                "parameters": [
                    {
                        "description": "QID == 群ID",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.OperateChatRoomInfoParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SetNickname": {
            "post": {
                "summary": "设置自己所在群的昵称",
                "parameters": [
                    {
                        "description": "QID == 群ID",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Group.SetNicknameParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SetPasswd": {
            "post": {
                "summary": "修改密码",
                "parameters": [
                    {
                        "description": "修改密码",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.NewSetPasswdParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/SetRemarks": {
            "post": {
                "summary": "设置好友备注",
                "parameters": [
                    {
                        "description": "设置好友备注",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Friend.SetRemarksParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ShareCard": {
            "post": {
                "summary": "分享名片",
                "parameters": [
                    {
                        "description": "ToWxid==接收的微信ID CardWxId==名片wxid CardNickName==名片昵称 CardAlias==名片别名 ",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.ShareCardParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ShareLink": {
            "post": {
                "summary": "发送分享链接消息",
                "parameters": [
                    {
                        "description": "Title==标题 Desc==描述 Url==跳转地址 ThumbUrl==图片展示",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.SendShareLinkMsgParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ShareLocation": {
            "post": {
                "summary": "分享位置",
                "parameters": [
                    {
                        "description": "分享位置",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.ShareLocationParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ShareVideo": {
            "post": {
                "summary": "发送分享视频消息",
                "parameters": [
                    {
                        "description": "xml：微信返回的视频xml",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.ShareVideoMsgParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Sync": {
            "post": {
                "summary": "同步消息",
                "parameters": [
                    {
                        "description": "同步消息参数：Scene填写1,SyncMsgDigest填写1 初始化消息：Scene填写7,SyncMsgDigest填写1",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.SyncParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/TargetUserPage": {
            "post": {
                "summary": "查看指定人首页",
                "parameters": [
                    {
                        "description": "查看指定人首页",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Finder.TargetUserPageParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/ThirdAppGrant": {
            "post": {
                "summary": "第三方APP授权",
                "parameters": [
                    {
                        "description": "注意参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Tools.ThirdAppGrantParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/TwiceAutoAuth": {
            "post": {
                "summary": "二次登陆",
                "parameters": [
                    {
                        "description": "二次登陆",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Login.OsParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/UpdateDeviceToken": {
            "post": {
                "summary": "DeviceToken更新",
                "parameters": [
                    {
                        "description": "DeviceToken更新",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Login.UpdateDeviceTokenParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/UpdateList": {
            "post": {
                "summary": "更新标签列表",
                "parameters": [
                    {
                        "description": "ToWxid:多个请用,隔开",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Label.UpdateListParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/UpdateName": {
            "post": {
                "summary": "修改标签",
                "parameters": [
                    {
                        "description": "修改标签",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Label.UpdateNameParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/UpdateProfile": {
            "post": {
                "summary": "修改个人信息",
                "parameters": [
                    {
                        "description": "NickName ==名称  Sex == 性别（1:男 2：女） Country == 国家,例如：CH Province == 省份 例如:WuHan Signature == 个性签名",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.UpdateProfileParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/Upload": {
            "post": {
                "summary": "朋友圈上传",
                "parameters": [
                    {
                        "description": "支持图片和视频",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FriendCircle.SnsUploadParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/UploadFile": {
            "post": {
                "summary": "文件上传",
                "parameters": [
                    {
                        "description": "文件上传",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Tools.UploadParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/UploadHeadImage": {
            "post": {
                "summary": "修改头像",
                "parameters": [
                    {
                        "description": "修改头像",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.UploadHeadImageParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/UploadImg": {
            "post": {
                "summary": "发送图片",
                "parameters": [
                    {
                        "description": "请注意base64格式",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Msg.SendImageMsgParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/UploadSep": {
            "post": {
                "summary": "朋友圈上传(分包模式)",
                "parameters": [
                    {
                        "description": "Base64 当前分包数据的base64, StartPos分包起始位置 TotalLen总数据大小 HashMd5整个文件的hashMd5, ClientID通信标识,每个包必须统一。 Type == 2是图片 5是视频",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FriendCircle.SnsUploadSepParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/UserPrepare": {
            "post": {
                "summary": "用户中心",
                "parameters": [
                    {
                        "type": "string",
                        "description": "请输登陆后的wxid",
                        "name": "wxid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/VerifyPasswd": {
            "post": {
                "summary": "验证密码",
                "parameters": [
                    {
                        "description": "验证密码",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/User.NewVerifyPasswdParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        },
        "/setproxy": {
            "post": {
                "summary": "设置/删除代理IP",
                "parameters": [
                    {
                        "description": "删除代理ip时直接留空即可",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Tools.SetProxyParam"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": ""
                    }
                }
            }
        }
    },
    "definitions": {
        "Algorithm.AndroidDeviceInfo": {
            "type": "object",
            "properties": {
                "andriodBssId": {
                    "type": "string"
                },
                "andriodFsId": {
                    "type": "string"
                },
                "andriodId": {
                    "type": "string"
                },
                "andriodSsId": {
                    "type": "string"
                },
                "androidversion": {
                    "type": "string"
                },
                "arch": {
                    "type": "string"
                },
                "buildBoard": {
                    "type": "string"
                },
                "buildFP": {
                    "type": "string"
                },
                "buildID": {
                    "type": "string"
                },
                "features": {
                    "type": "string"
                },
                "hardware": {
                    "type": "string"
                },
                "imei": {
                    "type": "string"
                },
                "kernelReleaseNumber": {
                    "type": "string"
                },
                "manufacturer": {
                    "type": "string"
                },
                "packageSign": {
                    "type": "string"
                },
                "phoneModel": {
                    "type": "string"
                },
                "phoneSerial": {
                    "type": "string"
                },
                "radioVersion": {
                    "type": "string"
                },
                "sbMD5": {
                    "type": "string"
                },
                "sfArm64MD5": {
                    "type": "string"
                },
                "sfArmMD5": {
                    "type": "string"
                },
                "sfMD5": {
                    "type": "string"
                },
                "widevineDeviceID": {
                    "type": "string"
                },
                "widevineProvisionID": {
                    "type": "string"
                },
                "wifiFullName": {
                    "type": "string"
                },
                "wifiName": {
                    "type": "string"
                },
                "wlanAddress": {
                    "type": "string"
                }
            }
        },
        "Favor.AddFavParam": {
            "type": "object",
            "properties": {
                "object": {
                    "type": "string"
                },
                "sourceId": {
                    "type": "string"
                },
                "sourceType": {
                    "type": "integer"
                },
                "type": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Favor.DelParam": {
            "type": "object",
            "properties": {
                "favId": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Favor.GetFavItemParam": {
            "type": "object",
            "properties": {
                "favId": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Favor.SyncParam": {
            "type": "object",
            "properties": {
                "keybuf": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Finder.CommentParam": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "objectNonceId": {
                    "type": "string"
                },
                "sessionBuffer": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Finder.DefaultParam": {
            "type": "object",
            "properties": {
                "finderUsername": {
                    "type": "string"
                },
                "value": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Finder.FinderGetTopicListParam": {
            "type": "object",
            "properties": {
                "lastBuffer": {
                    "type": "string"
                },
                "topTitle": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Finder.FinderLiveDetailParam": {
            "type": "object",
            "properties": {
                "finderNonceID": {
                    "type": "string"
                },
                "finderObjectID": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Finder.FinderSearchParam": {
            "type": "object",
            "properties": {
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Finder.FinderSendTextParam": {
            "type": "object",
            "properties": {
                "finderUsername": {
                    "type": "string"
                },
                "text": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Finder.GetCommentDetailParam": {
            "type": "object",
            "properties": {
                "finderUsername": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "lastBuffer": {
                    "type": "string"
                },
                "objectNonceId": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Finder.GetRecommendParam": {
            "type": "object",
            "properties": {
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Finder.LikeParam": {
            "type": "object",
            "properties": {
                "finderUsername": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "objectNonceId": {
                    "type": "string"
                },
                "sessionBuffer": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Finder.TargetUserPageParam": {
            "type": "object",
            "properties": {
                "lastBuffer": {
                    "type": "string"
                },
                "target": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Friend.BlacklistParam": {
            "type": "object",
            "properties": {
                "toWxid": {
                    "type": "string"
                },
                "val": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Friend.DefaultParam": {
            "type": "object",
            "properties": {
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Friend.GetContractDetailparameter": {
            "type": "object",
            "properties": {
                "chatRoom": {
                    "type": "string"
                },
                "towxids": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Friend.GetContractListparameter": {
            "type": "object",
            "properties": {
                "currentChatRoomContactSeq": {
                    "type": "integer"
                },
                "currentWxcontactSeq": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Friend.LbsFindParam": {
            "type": "object",
            "properties": {
                "latitude": {
                    "type": "number"
                },
                "longitude": {
                    "type": "number"
                },
                "opCode": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Friend.PassVerifyParam": {
            "type": "object",
            "properties": {
                "scene": {
                    "type": "integer"
                },
                "v1": {
                    "type": "string"
                },
                "v2": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Friend.SearchParam": {
            "type": "object",
            "properties": {
                "fromScene": {
                    "type": "integer"
                },
                "searchScene": {
                    "type": "integer"
                },
                "toUserName": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Friend.SendRequestParam": {
            "type": "object",
            "properties": {
                "opCode": {
                    "type": "integer"
                },
                "scene": {
                    "type": "integer"
                },
                "v1": {
                    "type": "string"
                },
                "v2": {
                    "type": "string"
                },
                "verifyContent": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Friend.SetRemarksParam": {
            "type": "object",
            "properties": {
                "remarks": {
                    "type": "string"
                },
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Friend.UploadParam": {
            "type": "object",
            "properties": {
                "currentPhoneNo": {
                    "type": "string"
                },
                "opcode": {
                    "type": "integer"
                },
                "phoneNo": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "FriendCircle.CommentParam": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "replyCommnetId": {
                    "type": "integer"
                },
                "type": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "FriendCircle.GetDetailparameter": {
            "type": "object",
            "properties": {
                "fristpagemd5": {
                    "type": "string"
                },
                "maxid": {
                    "type": "integer"
                },
                "towxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "FriendCircle.GetIdDetailParam": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "idStr": {
                    "type": "string"
                },
                "towxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "FriendCircle.GetListParam": {
            "type": "object",
            "properties": {
                "fristpagemd5": {
                    "type": "string"
                },
                "maxid": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "FriendCircle.Messagearameter": {
            "type": "object",
            "properties": {
                "blackList": {
                    "type": "string"
                },
                "content": {
                    "type": "string"
                },
                "isvideo": {
                    "type": "boolean"
                },
                "withUserList": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "FriendCircle.MmSnsSyncParam": {
            "type": "object",
            "properties": {
                "synckey": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "FriendCircle.OperationParam": {
            "type": "object",
            "properties": {
                "commnetId": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "type": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "FriendCircle.PrivacySettingsParam": {
            "type": "object",
            "properties": {
                "function": {
                    "type": "integer"
                },
                "value": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "FriendCircle.SnsUploadParam": {
            "type": "object",
            "properties": {
                "base64": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "FriendCircle.SnsUploadSepParam": {
            "type": "object",
            "properties": {
                "base64": {
                    "type": "string"
                },
                "clientID": {
                    "type": "string"
                },
                "hashMd5": {
                    "type": "string"
                },
                "startPos": {
                    "type": "integer"
                },
                "totalLen": {
                    "type": "integer"
                },
                "type": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.AddChatRoomParam": {
            "type": "object",
            "properties": {
                "chatRoomName": {
                    "type": "string"
                },
                "toWxids": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.ConsentToJoinParam": {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.CreateChatRoomParam": {
            "type": "object",
            "properties": {
                "toWxids": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.GetChatRoomParam": {
            "type": "object",
            "properties": {
                "qid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.GroupListParam": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.MoveContractListParam": {
            "type": "object",
            "properties": {
                "qid": {
                    "type": "string"
                },
                "val": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.OperateChatRoomAdminParam": {
            "type": "object",
            "properties": {
                "qid": {
                    "type": "string"
                },
                "toWxids": {
                    "type": "string"
                },
                "val": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.OperateChatRoomInfoParam": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "qid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.QidToWxidParam": {
            "type": "object",
            "properties": {
                "qid": {
                    "type": "string"
                },
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.QuitGroupParam": {
            "type": "object",
            "properties": {
                "qid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.ScanIntoGroupParam": {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Group.SetNicknameParam": {
            "type": "object",
            "properties": {
                "nickname": {
                    "type": "string"
                },
                "qid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Label.AddParam": {
            "type": "object",
            "properties": {
                "labelName": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Label.DeleteParam": {
            "type": "object",
            "properties": {
                "labelID": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Label.UpdateListParam": {
            "type": "object",
            "properties": {
                "labelID": {
                    "type": "string"
                },
                "toWxids": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Label.UpdateNameParam": {
            "type": "object",
            "properties": {
                "labelID": {
                    "type": "integer"
                },
                "newName": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Login.A16LoginParam": {
            "type": "object",
            "properties": {
                "a16": {
                    "type": "string"
                },
                "deviceName": {
                    "type": "string"
                },
                "extend": {
                    "$ref": "#/definitions/Algorithm.AndroidDeviceInfo"
                },
                "password": {
                    "type": "string"
                },
                "proxy": {
                    "$ref": "#/definitions/models.ProxyInfo"
                },
                "userName": {
                    "type": "string"
                }
            }
        },
        "Login.Data62LoginReq": {
            "type": "object",
            "properties": {
                "data62": {
                    "type": "string"
                },
                "deviceName": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "proxy": {
                    "$ref": "#/definitions/models.ProxyInfo"
                },
                "userName": {
                    "type": "string"
                }
            }
        },
        "Login.ExtDeviceLoginConfirmParam": {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Login.GetQRReq": {
            "type": "object",
            "properties": {
                "deviceID": {
                    "type": "string"
                },
                "deviceName": {
                    "type": "string"
                },
                "osmodel": {
                    "type": "string"
                },
                "proxy": {
                    "$ref": "#/definitions/models.ProxyInfo"
                }
            }
        },
        "Login.OsParam": {
            "type": "object",
            "properties": {
                "osmodel": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Login.PhoneDeviceLoginParam": {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Login.Rouses": {
            "type": "object",
            "properties": {
                "os": {
                    "type": "string"
                },
                "proxy": {
                    "$ref": "#/definitions/models.ProxyInfo"
                },
                "url": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Login.UpdateDeviceTokenParam": {
            "type": "object",
            "properties": {
                "model": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.BatchSendMsgParam": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "toWxids": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.DefaultParam": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.RemindMsgParam": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                },
                "ycontent": {
                    "type": "string"
                },
                "ytitle": {
                    "type": "string"
                }
            }
        },
        "Msg.RevokeMsgParam": {
            "type": "object",
            "properties": {
                "clientMsgId": {
                    "type": "integer"
                },
                "createTime": {
                    "type": "integer"
                },
                "newMsgId": {
                    "type": "integer"
                },
                "toUserName": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.SendAppMsgParam": {
            "type": "object",
            "properties": {
                "toWxid": {
                    "type": "string"
                },
                "type": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                },
                "xml": {
                    "type": "string"
                }
            }
        },
        "Msg.SendEmojiParam": {
            "type": "object",
            "properties": {
                "md5": {
                    "type": "string"
                },
                "toWxid": {
                    "type": "string"
                },
                "totalLen": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.SendImageMsgParam": {
            "type": "object",
            "properties": {
                "base64": {
                    "type": "string"
                },
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.SendNewMsgParam": {
            "type": "object",
            "properties": {
                "at": {
                    "type": "string"
                },
                "content": {
                    "type": "string"
                },
                "toWxid": {
                    "type": "string"
                },
                "type": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.SendShareLinkMsgParam": {
            "type": "object",
            "properties": {
                "desc": {
                    "type": "string"
                },
                "thumbUrl": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "toWxid": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.SendTransmitParam": {
            "type": "object",
            "properties": {
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                },
                "xml": {
                    "type": "string"
                }
            }
        },
        "Msg.SendVideoMsgParam": {
            "type": "object",
            "properties": {
                "base64": {
                    "type": "string"
                },
                "imageBase64": {
                    "type": "string"
                },
                "playLength": {
                    "type": "integer"
                },
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.SendVoiceMessageParam": {
            "type": "object",
            "properties": {
                "base64": {
                    "type": "string"
                },
                "toWxid": {
                    "type": "string"
                },
                "type": {
                    "type": "integer"
                },
                "voiceTime": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.ShareCardParam": {
            "type": "object",
            "properties": {
                "cardAlias": {
                    "type": "string"
                },
                "cardNickName": {
                    "type": "string"
                },
                "cardWxId": {
                    "type": "string"
                },
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Msg.ShareLocationParam": {
            "type": "object",
            "properties": {
                "infourl": {
                    "type": "string"
                },
                "label": {
                    "type": "string"
                },
                "poiname": {
                    "type": "string"
                },
                "scale": {
                    "type": "number"
                },
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                },
                "x": {
                    "type": "number"
                },
                "y": {
                    "type": "number"
                }
            }
        },
        "Msg.ShareVideoMsgParam": {
            "type": "object",
            "properties": {
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                },
                "xml": {
                    "type": "string"
                }
            }
        },
        "Msg.SyncParam": {
            "type": "object",
            "properties": {
                "scene": {
                    "type": "integer"
                },
                "synckey": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "OfficialAccounts.DefaultParam": {
            "type": "object",
            "properties": {
                "appid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "OfficialAccounts.GetkeyParam": {
            "type": "object",
            "properties": {
                "appid": {
                    "type": "string"
                },
                "jsapiList": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "nonceStr": {
                    "type": "string"
                },
                "signature": {
                    "type": "string"
                },
                "timestamp": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "OfficialAccounts.ReadParam": {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "SayHello.Model1Param": {
            "type": "object",
            "properties": {
                "opCode": {
                    "type": "integer"
                },
                "url": {
                    "type": "string"
                },
                "verifyContent": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "SayHello.Model2Param": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "fromScene": {
                    "type": "integer"
                },
                "opCode": {
                    "type": "integer"
                },
                "scene": {
                    "type": "integer"
                },
                "searchScene": {
                    "type": "integer"
                },
                "toUserName": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "TenPay.HongBaoParam": {
            "type": "object",
            "properties": {
                "qid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                },
                "xml": {
                    "type": "string"
                }
            }
        },
        "Tools.DownLoadEmojiParam": {
            "type": "object",
            "properties": {
                "md5": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Tools.DownloadAppAttachParam": {
            "type": "object",
            "properties": {
                "appID": {
                    "type": "string"
                },
                "attachId": {
                    "type": "string"
                },
                "dataLen": {
                    "type": "integer"
                },
                "section": {
                    "$ref": "#/definitions/Tools.Section"
                },
                "userName": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Tools.DownloadParam": {
            "type": "object",
            "properties": {
                "compressType": {
                    "type": "integer"
                },
                "dataLen": {
                    "type": "integer"
                },
                "msgId": {
                    "type": "integer"
                },
                "section": {
                    "$ref": "#/definitions/Tools.Section"
                },
                "toWxid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Tools.DownloadVoiceParam": {
            "type": "object",
            "properties": {
                "bufid": {
                    "type": "string"
                },
                "fromUserName": {
                    "type": "string"
                },
                "length": {
                    "type": "integer"
                },
                "msgId": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Tools.GetA8KeyParam": {
            "type": "object",
            "properties": {
                "codeType": {
                    "type": "integer"
                },
                "codeVersion": {
                    "type": "integer"
                },
                "opCode": {
                    "type": "integer"
                },
                "reqUrl": {
                    "type": "string"
                },
                "scene": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Tools.Section": {
            "type": "object",
            "properties": {
                "dataLen": {
                    "type": "integer"
                },
                "startPos": {
                    "type": "integer"
                }
            }
        },
        "Tools.SetProxyParam": {
            "type": "object",
            "properties": {
                "proxy": {
                    "$ref": "#/definitions/models.ProxyInfo"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Tools.ThirdAppGrantParam": {
            "type": "object",
            "properties": {
                "appid": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Tools.UploadParam": {
            "type": "object",
            "properties": {
                "base64": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.BindMobileParam": {
            "type": "object",
            "properties": {
                "mobile": {
                    "type": "string"
                },
                "verifycode": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.DelSafetyInfoParam": {
            "type": "object",
            "properties": {
                "uuid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.EmailParam": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.GetQRCodeParam": {
            "type": "object",
            "properties": {
                "style": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.NewSetPasswdParam": {
            "type": "object",
            "properties": {
                "newPassword": {
                    "type": "string"
                },
                "ticket": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.NewVerifyPasswdParam": {
            "type": "object",
            "properties": {
                "password": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.OnekeySetPasswdParam": {
            "type": "object",
            "properties": {
                "newPassword": {
                    "type": "string"
                },
                "oldPassword": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.PrivacySettingsParam": {
            "type": "object",
            "properties": {
                "function": {
                    "type": "integer"
                },
                "value": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.ReportMotionParam": {
            "type": "object",
            "properties": {
                "deviceId": {
                    "type": "string"
                },
                "deviceType": {
                    "type": "string"
                },
                "stepCount": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.ReviseAlisa": {
            "type": "object",
            "properties": {
                "newsAlisa": {
                    "type": "string"
                },
                "pwdTicket": {
                    "type": "string"
                },
                "safeTicket": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.SendVerifyMobileParam": {
            "type": "object",
            "properties": {
                "mobile": {
                    "type": "string"
                },
                "opcode": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.SetAlisaParam": {
            "type": "object",
            "properties": {
                "alisa": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.UpdateProfileParam": {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string"
                },
                "country": {
                    "type": "string"
                },
                "nickName": {
                    "type": "string"
                },
                "province": {
                    "type": "string"
                },
                "sex": {
                    "type": "integer"
                },
                "signature": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "User.UploadHeadImageParam": {
            "type": "object",
            "properties": {
                "base64": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Wxapp.DefaultParam": {
            "type": "object",
            "properties": {
                "appid": {
                    "type": "string"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "Wxapp.JSOperateWxParam": {
            "type": "object",
            "properties": {
                "appid": {
                    "type": "string"
                },
                "data": {
                    "type": "string"
                },
                "opt": {
                    "type": "integer"
                },
                "wxid": {
                    "type": "string"
                }
            }
        },
        "models.ProxyInfo": {
            "type": "object",
            "properties": {
                "isHttp": {
                    "type": "boolean"
                },
                "proxyIp": {
                    "type": "string"
                },
                "proxyPassword": {
                    "type": "string"
                },
                "proxyUser": {
                    "type": "string"
                }
            }
        }
    }
}`

type swaggerInfo struct {
	Version     string
	Host        string
	BasePath    string
	Schemes     []string
	Title       string
	Description string
}

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = swaggerInfo{
	Version:     "",
	Host:        "",
	BasePath:    "",
	Schemes:     []string{},
	Title:       "",
	Description: "",
}

type s struct{}

func (s *s) ReadDoc() string {
	sInfo := SwaggerInfo
	sInfo.Description = strings.Replace(sInfo.Description, "\n", "\\n", -1)

	t, err := template.New("swagger_info").Funcs(template.FuncMap{
		"marshal": func(v interface{}) string {
			a, _ := json.Marshal(v)
			return string(a)
		},
	}).Parse(doc)
	if err != nil {
		return doc
	}

	var tpl bytes.Buffer
	if err := t.Execute(&tpl, sInfo); err != nil {
		return doc
	}

	return tpl.String()
}

func init() {
	swag.Register(swag.Name, &s{})
}
