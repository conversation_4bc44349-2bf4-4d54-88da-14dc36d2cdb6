syntax="proto2";

package gg;

message FinderGetCommentDetailRsp {
  repeated FinderCommentInfo CommentInfo = 2;
  optional FinderObject Object = 3;
  optional bytes LastBuffer = 4;
  optional uint32 CommentCount = 5;
  optional uint32 UpContinueFlag = 6;
  optional uint32 DownContinueFlag = 7;
  optional uint32 NextCheckObjectStatus = 8;
  optional uint32 SecondaryShowFlag = 9;
}


message FinderCommentInfo {
  optional  string username = 1;
  optional  string nickname = 2;
  optional  string content = 3;
  optional  uint32 commentId = 4;
  optional  uint32 replyCommentId = 5;
  optional  int32 deleteFlag = 6;
  optional  string headUrl = 7;
  repeated  LevelTwoComment levelTwoComment = 8;
  optional  uint32 createtime = 9;
  optional  string replyNickname = 10;
  optional  string displayidDiscarded = 11;
  optional  int32 likeFlag = 12;
  optional  int32 likeCount = 13;
  optional  uint32 displayid = 14;
  optional  int32 expandCommentCount = 15;
  optional  bytes lastBuffer = 16;
  optional  int32 continueFlag = 17;
  optional  int32 displayFlag = 18;
  optional  int32 blacklistFlag = 19;
  optional  string replyContent = 20;
  optional  string replyUsername = 21;
  optional  string clientId = 22;
  optional  int32 upContinueFlag = 23;
}
message LevelTwoComment{
  optional string v1=1;
  optional string v2=2;
  optional string v3=3;
  optional uint64 v4=4;
  optional uint64 v5=5;
  optional uint64 v6=6;
  optional string v7=7;
  optional uint64 v9=9;
  optional string v10=10;
  optional uint64 v12=12;
  optional uint64 v13=13;
  optional uint64 v14=14;
  optional uint64 v18=18;
  optional string v20=20;
  optional string v21=21;
}

message FinderObject {
  optional  uint64 id = 1;
  optional  string nickname = 2;
  optional  string username = 3;
  optional  FinderObjectDesc objectDesc = 4;
  optional  int32 createtime = 5;
  optional  int32 likeFlag = 6;
  repeated  bytes likeList = 7; //未找到相关PB
  repeated  FinderCommentInfo commentList = 8;
  optional  int32 forwardCount = 9;
  optional  FinderContact contact = 10;
  optional  string displayidDiscarded = 11;
  repeated  FinderRecommendInfo recommenderList = 12; //疑似PB不对
  optional  uint64 displayid = 13;
  optional  int32 likeCount = 14;
  optional  int32 commentCount = 15;
  optional  string recommendReason = 16;
  optional  int32 readCount = 17;
  optional  int32 deletetime = 18;
  optional  int32 commentClose = 19;
  optional  uint32 refObjectFlag = 20;
  optional  uint32 refObjectid = 21;
  optional  FinderContact refObjectContact = 22;
  optional  int32 recommendType = 23;
  optional  int32 friendLikeCount = 24;
  optional  string objectNonceId = 25;
  optional  string refObjectNonceId = 26;
  optional  int32 objectStatus = 27;
  optional  string sendShareFavWording = 28;
  optional  int32 originalFlag = 29;
  optional  int32 secondaryShowFlag = 30;
  optional  string tipsWording = 31;
  optional  int32 orgRecommendType = 32;
  optional string enKey=34;
  optional uint64 unk38=38;
  optional uint64 unk39=39;
  optional uint64 unk43=43;
  optional uint64 unk44=44;
  optional uint64 unk57=57;
}
message FinderRecommendInfo {
  optional  string tid = 1;
  optional  uint32 recommendType = 2;
  optional  string recommendReason = 3;
  optional  uint32 orgRecommendType = 4;
  optional  uint32 lastInsertedRowID = 5;
  optional  bool isAutoIncrement = 6;
}
message FinderContact {
  optional  string username = 1;
  optional  string nickname = 2;
  optional  string headUrl = 3;
  optional  uint64 seq = 4;
  optional  string signature = 5;
  optional  int32 followFlag = 6;
  optional  int32 followTime = 7;
  optional  FinderAuthInfo authInfo = 8;
  optional  string coverImgUrl = 9;
  optional  int32 spamStatus = 10;
  optional  int32 extFlag = 11;
  optional  FinderContactExtInfo extInfo = 12;
  optional  int32 originalFlag = 13;
}
message FinderContactExtInfo {
  optional  string country = 1;
  optional  string province = 2;
  optional  string city = 3;
  optional  int32 sex = 4;
  optional  int32 birthYear = 5;
  optional  int32 birthMonth = 6;
  optional  int32 birthDay = 7;
}
message FinderAuthInfo {
  /* optional  string realName = 1;
   optional  int32 authIconType = 2;
   optional  string authProfession = 3;
   optional  FinderContact authGuarantor = 4;
   optional  string detailLink = 5;
   optional  string appName = 6;*/
  optional string v1=1;
  optional  int32 authIconType = 2;
  optional  string realName = 3;
  optional  string photo = 7;
  optional  int32 v8 = 8;
  optional  int32 v9 = 9;
}

message FinderObjectDesc {
  optional  string description = 1;
  repeated  FinderMedia media = 2;
  optional  int32 mediaType = 3;
  optional  FinderMediaExtra extra = 4;
  optional  FinderLocation location = 5;
  optional  FinderExtendedReading extReading = 6;
  optional  FinderTopic topic = 7;
}
message FinderExtendedReading {
  optional  string link = 1;
  optional  string title = 2;
}

message FinderTopic {
  optional  string finderTopicInfo = 1;
}
message FinderLocation {
  optional  float longitude = 1;
  optional  float latitude = 2;
  optional  string city = 3;
  optional  string poiName = 4;
  optional  string poiAddress = 5;
  optional  string poiClassifyId = 6;
  optional  int32 poiClassifyType = 7;
}

message FinderMediaExtra {
  optional  string text = 1;
}
message FinderMedia {
  optional  string Url = 1;
  optional  string ThumbUrl = 2;
  optional  int32 MediaType = 3;
  optional  int32 VideoPlayLen = 4;
  optional  float Width = 5;
  optional  float Height = 6;
  optional  string Md5Sum = 7;
  optional  int32 FileSize = 8;
  optional  int32 Bitrate = 9;
  repeated  FinderMediaSpec Spec = 10;
  optional string FinderMediaV11=11;
  optional string FinderMediaV12=12;
  optional string FinderMediaV13Token=13;
  optional string FinderMediaV14Token=14;
  optional string FinderMediaV21=21;
  optional string FinderMediaV22=22;
}
message FinderMediaSpec {
  optional  string fileFormat = 1;
  optional  int32 firstLoadBytes = 2;
  optional  int32 bitRate = 3;
  optional  string codingFormat = 4;
}