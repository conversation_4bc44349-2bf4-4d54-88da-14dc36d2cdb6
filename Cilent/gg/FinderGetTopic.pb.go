// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        (unknown)
// source: FinderGetTopic.proto

package gg

import (
	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type FinderGetTopicListResponse struct {
	FinderObject []*FinderObject `protobuf:"bytes,2,rep,name=finderObject" json:"finderObject,omitempty"`
	LastBuffer   []byte          `protobuf:"bytes,3,opt,name=LastBuffer" json:"LastBuffer,omitempty"`
	RspV4        *uint64         `protobuf:"varint,4,opt,name=rspV4" json:"rspV4,omitempty"`
	RspV5        *uint64         `protobuf:"varint,5,opt,name=rspV5" json:"rspV5,omitempty"`
	RspV6        []byte          `protobuf:"bytes,6,opt,name=rspV6" json:"rspV6,omitempty"`
}

func (x *FinderGetTopicListResponse) Reset() {
	*x = FinderGetTopicListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetTopic_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderGetTopicListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderGetTopicListResponse) ProtoMessage() {}

func (x *FinderGetTopicListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetTopic_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderGetTopicListResponse.ProtoReflect.Descriptor instead.
func (*FinderGetTopicListResponse) Descriptor() ([]byte, []int) {
	return file_FinderGetTopic_proto_rawDescGZIP(), []int{0}
}

func (x *FinderGetTopicListResponse) GetFinderObject() []*FinderObject {
	if x != nil {
		return x.FinderObject
	}
	return nil
}

func (x *FinderGetTopicListResponse) GetLastBuffer() []byte {
	if x != nil {
		return x.LastBuffer
	}
	return nil
}

func (x *FinderGetTopicListResponse) GetRspV4() uint64 {
	if x != nil && x.RspV4 != nil {
		return *x.RspV4
	}
	return 0
}

func (x *FinderGetTopicListResponse) GetRspV5() uint64 {
	if x != nil && x.RspV5 != nil {
		return *x.RspV5
	}
	return 0
}

func (x *FinderGetTopicListResponse) GetRspV6() []byte {
	if x != nil {
		return x.RspV6
	}
	return nil
}

var File_FinderGetTopic_proto protoreflect.FileDescriptor

var file_FinderGetTopic_proto_rawDesc = []byte{
	0x0a, 0x14, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x67, 0x67, 0x1a, 0x1c, 0x46, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85, 0x01, 0x0a, 0x1a, 0x46, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x67, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12,
	0x12, 0x0a, 0x0a, 0x4c, 0x61, 0x73, 0x74, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0c, 0x12, 0x0d, 0x0a, 0x05, 0x72, 0x73, 0x70, 0x56, 0x34, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x12, 0x0d, 0x0a, 0x05, 0x72, 0x73, 0x70, 0x56, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x04, 0x12, 0x0d, 0x0a, 0x05, 0x72, 0x73, 0x70, 0x56, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c,
}

var (
	file_FinderGetTopic_proto_rawDescOnce sync.Once
	file_FinderGetTopic_proto_rawDescData = file_FinderGetTopic_proto_rawDesc
)

func file_FinderGetTopic_proto_rawDescGZIP() []byte {
	file_FinderGetTopic_proto_rawDescOnce.Do(func() {
		file_FinderGetTopic_proto_rawDescData = protoimpl.X.CompressGZIP(file_FinderGetTopic_proto_rawDescData)
	})
	return file_FinderGetTopic_proto_rawDescData
}

var file_FinderGetTopic_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_FinderGetTopic_proto_goTypes = []interface{}{
	(*FinderGetTopicListResponse)(nil), // 0: gg.FinderGetTopicListResponse
	(*FinderObject)(nil),               // 1: gg.FinderObject
}
var file_FinderGetTopic_proto_depIdxs = []int32{
	1, // 0: gg.FinderGetTopicListResponse.finderObject:type_name -> gg.FinderObject
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_FinderGetTopic_proto_init() }
func file_FinderGetTopic_proto_init() {
	if File_FinderGetTopic_proto != nil {
		return
	}
	file_FinderGetCommentDetail_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_FinderGetTopic_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_FinderGetTopic_proto_goTypes,
		DependencyIndexes: file_FinderGetTopic_proto_depIdxs,
		MessageInfos:      file_FinderGetTopic_proto_msgTypes,
	}.Build()
	File_FinderGetTopic_proto = out.File
	file_FinderGetTopic_proto_rawDesc = nil
	file_FinderGetTopic_proto_goTypes = nil
	file_FinderGetTopic_proto_depIdxs = nil
}
