// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        (unknown)
// source: FinderGetCommentDetail.proto

package gg

import (
	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type FinderGetCommentDetailRsp struct {
	CommentInfo           []*FinderCommentInfo `protobuf:"bytes,2,rep,name=CommentInfo" json:"CommentInfo,omitempty"`
	Object                *FinderObject        `protobuf:"bytes,3,opt,name=Object" json:"Object,omitempty"`
	LastBuffer            []byte               `protobuf:"bytes,4,opt,name=LastBuffer" json:"LastBuffer,omitempty"`
	CommentCount          *uint32              `protobuf:"varint,5,opt,name=CommentCount" json:"CommentCount,omitempty"`
	UpContinueFlag        *uint32              `protobuf:"varint,6,opt,name=UpContinueFlag" json:"UpContinueFlag,omitempty"`
	DownContinueFlag      *uint32              `protobuf:"varint,7,opt,name=DownContinueFlag" json:"DownContinueFlag,omitempty"`
	NextCheckObjectStatus *uint32              `protobuf:"varint,8,opt,name=NextCheckObjectStatus" json:"NextCheckObjectStatus,omitempty"`
	SecondaryShowFlag     *uint32              `protobuf:"varint,9,opt,name=SecondaryShowFlag" json:"SecondaryShowFlag,omitempty"`
}

func (x *FinderGetCommentDetailRsp) Reset() {
	*x = FinderGetCommentDetailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderGetCommentDetailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderGetCommentDetailRsp) ProtoMessage() {}

func (x *FinderGetCommentDetailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderGetCommentDetailRsp.ProtoReflect.Descriptor instead.
func (*FinderGetCommentDetailRsp) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{0}
}

func (x *FinderGetCommentDetailRsp) GetCommentInfo() []*FinderCommentInfo {
	if x != nil {
		return x.CommentInfo
	}
	return nil
}

func (x *FinderGetCommentDetailRsp) GetObject() *FinderObject {
	if x != nil {
		return x.Object
	}
	return nil
}

func (x *FinderGetCommentDetailRsp) GetLastBuffer() []byte {
	if x != nil {
		return x.LastBuffer
	}
	return nil
}

func (x *FinderGetCommentDetailRsp) GetCommentCount() uint32 {
	if x != nil && x.CommentCount != nil {
		return *x.CommentCount
	}
	return 0
}

func (x *FinderGetCommentDetailRsp) GetUpContinueFlag() uint32 {
	if x != nil && x.UpContinueFlag != nil {
		return *x.UpContinueFlag
	}
	return 0
}

func (x *FinderGetCommentDetailRsp) GetDownContinueFlag() uint32 {
	if x != nil && x.DownContinueFlag != nil {
		return *x.DownContinueFlag
	}
	return 0
}

func (x *FinderGetCommentDetailRsp) GetNextCheckObjectStatus() uint32 {
	if x != nil && x.NextCheckObjectStatus != nil {
		return *x.NextCheckObjectStatus
	}
	return 0
}

func (x *FinderGetCommentDetailRsp) GetSecondaryShowFlag() uint32 {
	if x != nil && x.SecondaryShowFlag != nil {
		return *x.SecondaryShowFlag
	}
	return 0
}

type FinderCommentInfo struct {
	Username           *string            `protobuf:"bytes,1,opt,name=username" json:"username,omitempty"`
	Nickname           *string            `protobuf:"bytes,2,opt,name=nickname" json:"nickname,omitempty"`
	Content            *string            `protobuf:"bytes,3,opt,name=content" json:"content,omitempty"`
	CommentId          *uint32            `protobuf:"varint,4,opt,name=commentId" json:"commentId,omitempty"`
	ReplyCommentId     *uint32            `protobuf:"varint,5,opt,name=replyCommentId" json:"replyCommentId,omitempty"`
	DeleteFlag         *int32             `protobuf:"varint,6,opt,name=deleteFlag" json:"deleteFlag,omitempty"`
	HeadUrl            *string            `protobuf:"bytes,7,opt,name=headUrl" json:"headUrl,omitempty"`
	LevelTwoComment    []*LevelTwoComment `protobuf:"bytes,8,rep,name=levelTwoComment" json:"levelTwoComment,omitempty"`
	Createtime         *uint32            `protobuf:"varint,9,opt,name=createtime" json:"createtime,omitempty"`
	ReplyNickname      *string            `protobuf:"bytes,10,opt,name=replyNickname" json:"replyNickname,omitempty"`
	DisplayidDiscarded *string            `protobuf:"bytes,11,opt,name=displayidDiscarded" json:"displayidDiscarded,omitempty"`
	LikeFlag           *int32             `protobuf:"varint,12,opt,name=likeFlag" json:"likeFlag,omitempty"`
	LikeCount          *int32             `protobuf:"varint,13,opt,name=likeCount" json:"likeCount,omitempty"`
	Displayid          *uint32            `protobuf:"varint,14,opt,name=displayid" json:"displayid,omitempty"`
	ExpandCommentCount *int32             `protobuf:"varint,15,opt,name=expandCommentCount" json:"expandCommentCount,omitempty"`
	LastBuffer         []byte             `protobuf:"bytes,16,opt,name=lastBuffer" json:"lastBuffer,omitempty"`
	ContinueFlag       *int32             `protobuf:"varint,17,opt,name=continueFlag" json:"continueFlag,omitempty"`
	DisplayFlag        *int32             `protobuf:"varint,18,opt,name=displayFlag" json:"displayFlag,omitempty"`
	BlacklistFlag      *int32             `protobuf:"varint,19,opt,name=blacklistFlag" json:"blacklistFlag,omitempty"`
	ReplyContent       *string            `protobuf:"bytes,20,opt,name=replyContent" json:"replyContent,omitempty"`
	ReplyUsername      *string            `protobuf:"bytes,21,opt,name=replyUsername" json:"replyUsername,omitempty"`
	ClientId           *string            `protobuf:"bytes,22,opt,name=clientId" json:"clientId,omitempty"`
	UpContinueFlag     *int32             `protobuf:"varint,23,opt,name=upContinueFlag" json:"upContinueFlag,omitempty"`
}

func (x *FinderCommentInfo) Reset() {
	*x = FinderCommentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderCommentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderCommentInfo) ProtoMessage() {}

func (x *FinderCommentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderCommentInfo.ProtoReflect.Descriptor instead.
func (*FinderCommentInfo) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{1}
}

func (x *FinderCommentInfo) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *FinderCommentInfo) GetNickname() string {
	if x != nil && x.Nickname != nil {
		return *x.Nickname
	}
	return ""
}

func (x *FinderCommentInfo) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

func (x *FinderCommentInfo) GetCommentId() uint32 {
	if x != nil && x.CommentId != nil {
		return *x.CommentId
	}
	return 0
}

func (x *FinderCommentInfo) GetReplyCommentId() uint32 {
	if x != nil && x.ReplyCommentId != nil {
		return *x.ReplyCommentId
	}
	return 0
}

func (x *FinderCommentInfo) GetDeleteFlag() int32 {
	if x != nil && x.DeleteFlag != nil {
		return *x.DeleteFlag
	}
	return 0
}

func (x *FinderCommentInfo) GetHeadUrl() string {
	if x != nil && x.HeadUrl != nil {
		return *x.HeadUrl
	}
	return ""
}

func (x *FinderCommentInfo) GetLevelTwoComment() []*LevelTwoComment {
	if x != nil {
		return x.LevelTwoComment
	}
	return nil
}

func (x *FinderCommentInfo) GetCreatetime() uint32 {
	if x != nil && x.Createtime != nil {
		return *x.Createtime
	}
	return 0
}

func (x *FinderCommentInfo) GetReplyNickname() string {
	if x != nil && x.ReplyNickname != nil {
		return *x.ReplyNickname
	}
	return ""
}

func (x *FinderCommentInfo) GetDisplayidDiscarded() string {
	if x != nil && x.DisplayidDiscarded != nil {
		return *x.DisplayidDiscarded
	}
	return ""
}

func (x *FinderCommentInfo) GetLikeFlag() int32 {
	if x != nil && x.LikeFlag != nil {
		return *x.LikeFlag
	}
	return 0
}

func (x *FinderCommentInfo) GetLikeCount() int32 {
	if x != nil && x.LikeCount != nil {
		return *x.LikeCount
	}
	return 0
}

func (x *FinderCommentInfo) GetDisplayid() uint32 {
	if x != nil && x.Displayid != nil {
		return *x.Displayid
	}
	return 0
}

func (x *FinderCommentInfo) GetExpandCommentCount() int32 {
	if x != nil && x.ExpandCommentCount != nil {
		return *x.ExpandCommentCount
	}
	return 0
}

func (x *FinderCommentInfo) GetLastBuffer() []byte {
	if x != nil {
		return x.LastBuffer
	}
	return nil
}

func (x *FinderCommentInfo) GetContinueFlag() int32 {
	if x != nil && x.ContinueFlag != nil {
		return *x.ContinueFlag
	}
	return 0
}

func (x *FinderCommentInfo) GetDisplayFlag() int32 {
	if x != nil && x.DisplayFlag != nil {
		return *x.DisplayFlag
	}
	return 0
}

func (x *FinderCommentInfo) GetBlacklistFlag() int32 {
	if x != nil && x.BlacklistFlag != nil {
		return *x.BlacklistFlag
	}
	return 0
}

func (x *FinderCommentInfo) GetReplyContent() string {
	if x != nil && x.ReplyContent != nil {
		return *x.ReplyContent
	}
	return ""
}

func (x *FinderCommentInfo) GetReplyUsername() string {
	if x != nil && x.ReplyUsername != nil {
		return *x.ReplyUsername
	}
	return ""
}

func (x *FinderCommentInfo) GetClientId() string {
	if x != nil && x.ClientId != nil {
		return *x.ClientId
	}
	return ""
}

func (x *FinderCommentInfo) GetUpContinueFlag() int32 {
	if x != nil && x.UpContinueFlag != nil {
		return *x.UpContinueFlag
	}
	return 0
}

type LevelTwoComment struct {
	V1  *string `protobuf:"bytes,1,opt,name=v1" json:"v1,omitempty"`
	V2  *string `protobuf:"bytes,2,opt,name=v2" json:"v2,omitempty"`
	V3  *string `protobuf:"bytes,3,opt,name=v3" json:"v3,omitempty"`
	V4  *uint64 `protobuf:"varint,4,opt,name=v4" json:"v4,omitempty"`
	V5  *uint64 `protobuf:"varint,5,opt,name=v5" json:"v5,omitempty"`
	V6  *uint64 `protobuf:"varint,6,opt,name=v6" json:"v6,omitempty"`
	V7  *string `protobuf:"bytes,7,opt,name=v7" json:"v7,omitempty"`
	V9  *uint64 `protobuf:"varint,9,opt,name=v9" json:"v9,omitempty"`
	V10 *string `protobuf:"bytes,10,opt,name=v10" json:"v10,omitempty"`
	V12 *uint64 `protobuf:"varint,12,opt,name=v12" json:"v12,omitempty"`
	V13 *uint64 `protobuf:"varint,13,opt,name=v13" json:"v13,omitempty"`
	V14 *uint64 `protobuf:"varint,14,opt,name=v14" json:"v14,omitempty"`
	V18 *uint64 `protobuf:"varint,18,opt,name=v18" json:"v18,omitempty"`
	V20 *string `protobuf:"bytes,20,opt,name=v20" json:"v20,omitempty"`
	V21 *string `protobuf:"bytes,21,opt,name=v21" json:"v21,omitempty"`
}

func (x *LevelTwoComment) Reset() {
	*x = LevelTwoComment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LevelTwoComment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LevelTwoComment) ProtoMessage() {}

func (x *LevelTwoComment) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LevelTwoComment.ProtoReflect.Descriptor instead.
func (*LevelTwoComment) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{2}
}

func (x *LevelTwoComment) GetV1() string {
	if x != nil && x.V1 != nil {
		return *x.V1
	}
	return ""
}

func (x *LevelTwoComment) GetV2() string {
	if x != nil && x.V2 != nil {
		return *x.V2
	}
	return ""
}

func (x *LevelTwoComment) GetV3() string {
	if x != nil && x.V3 != nil {
		return *x.V3
	}
	return ""
}

func (x *LevelTwoComment) GetV4() uint64 {
	if x != nil && x.V4 != nil {
		return *x.V4
	}
	return 0
}

func (x *LevelTwoComment) GetV5() uint64 {
	if x != nil && x.V5 != nil {
		return *x.V5
	}
	return 0
}

func (x *LevelTwoComment) GetV6() uint64 {
	if x != nil && x.V6 != nil {
		return *x.V6
	}
	return 0
}

func (x *LevelTwoComment) GetV7() string {
	if x != nil && x.V7 != nil {
		return *x.V7
	}
	return ""
}

func (x *LevelTwoComment) GetV9() uint64 {
	if x != nil && x.V9 != nil {
		return *x.V9
	}
	return 0
}

func (x *LevelTwoComment) GetV10() string {
	if x != nil && x.V10 != nil {
		return *x.V10
	}
	return ""
}

func (x *LevelTwoComment) GetV12() uint64 {
	if x != nil && x.V12 != nil {
		return *x.V12
	}
	return 0
}

func (x *LevelTwoComment) GetV13() uint64 {
	if x != nil && x.V13 != nil {
		return *x.V13
	}
	return 0
}

func (x *LevelTwoComment) GetV14() uint64 {
	if x != nil && x.V14 != nil {
		return *x.V14
	}
	return 0
}

func (x *LevelTwoComment) GetV18() uint64 {
	if x != nil && x.V18 != nil {
		return *x.V18
	}
	return 0
}

func (x *LevelTwoComment) GetV20() string {
	if x != nil && x.V20 != nil {
		return *x.V20
	}
	return ""
}

func (x *LevelTwoComment) GetV21() string {
	if x != nil && x.V21 != nil {
		return *x.V21
	}
	return ""
}

type FinderObject struct {
	Id                  *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	FinderObjectID      *string                `protobuf:"bytes,2,opt,name=finderObjectID" json:"finderObjectID,omitempty"`
	Nickname            *string                `protobuf:"bytes,2,opt,name=nickname" json:"nickname,omitempty"`
	Username            *string                `protobuf:"bytes,3,opt,name=username" json:"username,omitempty"`
	ObjectDesc          *FinderObjectDesc      `protobuf:"bytes,4,opt,name=objectDesc" json:"objectDesc,omitempty"`
	Createtime          *int32                 `protobuf:"varint,5,opt,name=createtime" json:"createtime,omitempty"`
	LikeFlag            *int32                 `protobuf:"varint,6,opt,name=likeFlag" json:"likeFlag,omitempty"`
	LikeList            [][]byte               `protobuf:"bytes,7,rep,name=likeList" json:"likeList,omitempty"` //未找到相关PB
	CommentList         []*FinderCommentInfo   `protobuf:"bytes,8,rep,name=commentList" json:"commentList,omitempty"`
	ForwardCount        *int32                 `protobuf:"varint,9,opt,name=forwardCount" json:"forwardCount,omitempty"`
	Contact             *FinderContact         `protobuf:"bytes,10,opt,name=contact" json:"contact,omitempty"`
	DisplayidDiscarded  *string                `protobuf:"bytes,11,opt,name=displayidDiscarded" json:"displayidDiscarded,omitempty"`
	RecommenderList     []*FinderRecommendInfo `protobuf:"bytes,12,rep,name=recommenderList" json:"recommenderList,omitempty"` //疑似PB不对
	Displayid           *uint64                `protobuf:"varint,13,opt,name=displayid" json:"displayid,omitempty"`
	LikeCount           *int32                 `protobuf:"varint,14,opt,name=likeCount" json:"likeCount,omitempty"`
	CommentCount        *int32                 `protobuf:"varint,15,opt,name=commentCount" json:"commentCount,omitempty"`
	RecommendReason     *string                `protobuf:"bytes,16,opt,name=recommendReason" json:"recommendReason,omitempty"`
	ReadCount           *int32                 `protobuf:"varint,17,opt,name=readCount" json:"readCount,omitempty"`
	Deletetime          *int32                 `protobuf:"varint,18,opt,name=deletetime" json:"deletetime,omitempty"`
	CommentClose        *int32                 `protobuf:"varint,19,opt,name=commentClose" json:"commentClose,omitempty"`
	RefObjectFlag       *uint32                `protobuf:"varint,20,opt,name=refObjectFlag" json:"refObjectFlag,omitempty"`
	RefObjectid         *uint32                `protobuf:"varint,21,opt,name=refObjectid" json:"refObjectid,omitempty"`
	RefObjectContact    *FinderContact         `protobuf:"bytes,22,opt,name=refObjectContact" json:"refObjectContact,omitempty"`
	RecommendType       *int32                 `protobuf:"varint,23,opt,name=recommendType" json:"recommendType,omitempty"`
	FriendLikeCount     *int32                 `protobuf:"varint,24,opt,name=friendLikeCount" json:"friendLikeCount,omitempty"`
	ObjectNonceId       *string                `protobuf:"bytes,25,opt,name=objectNonceId" json:"objectNonceId,omitempty"`
	RefObjectNonceId    *string                `protobuf:"bytes,26,opt,name=refObjectNonceId" json:"refObjectNonceId,omitempty"`
	ObjectStatus        *int32                 `protobuf:"varint,27,opt,name=objectStatus" json:"objectStatus,omitempty"`
	SendShareFavWording *string                `protobuf:"bytes,28,opt,name=sendShareFavWording" json:"sendShareFavWording,omitempty"`
	OriginalFlag        *int32                 `protobuf:"varint,29,opt,name=originalFlag" json:"originalFlag,omitempty"`
	SecondaryShowFlag   *int32                 `protobuf:"varint,30,opt,name=secondaryShowFlag" json:"secondaryShowFlag,omitempty"`
	TipsWording         *string                `protobuf:"bytes,31,opt,name=tipsWording" json:"tipsWording,omitempty"`
	OrgRecommendType    *int32                 `protobuf:"varint,32,opt,name=orgRecommendType" json:"orgRecommendType,omitempty"`
	EnKey               *string                `protobuf:"bytes,34,opt,name=enKey" json:"enKey,omitempty"`
	Unk38               *uint64                `protobuf:"varint,38,opt,name=unk38" json:"unk38,omitempty"`
	Unk39               *uint64                `protobuf:"varint,39,opt,name=unk39" json:"unk39,omitempty"`
	Unk43               *uint64                `protobuf:"varint,43,opt,name=unk43" json:"unk43,omitempty"`
	Unk44               *uint64                `protobuf:"varint,44,opt,name=unk44" json:"unk44,omitempty"`
	Unk57               *uint64                `protobuf:"varint,57,opt,name=unk57" json:"unk57,omitempty"`
}

func (x *FinderObject) Reset() {
	*x = FinderObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderObject) ProtoMessage() {}

func (x *FinderObject) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderObject.ProtoReflect.Descriptor instead.
func (*FinderObject) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{3}
}

func (x *FinderObject) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *FinderObject) GetNickname() string {
	if x != nil && x.Nickname != nil {
		return *x.Nickname
	}
	return ""
}

func (x *FinderObject) GetFinderObjectID() string {
	if x != nil && x.FinderObjectID != nil {
		return *x.FinderObjectID
	}
	return ""
}

func (x *FinderObject) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *FinderObject) GetObjectDesc() *FinderObjectDesc {
	if x != nil {
		return x.ObjectDesc
	}
	return nil
}

func (x *FinderObject) GetCreatetime() int32 {
	if x != nil && x.Createtime != nil {
		return *x.Createtime
	}
	return 0
}

func (x *FinderObject) GetLikeFlag() int32 {
	if x != nil && x.LikeFlag != nil {
		return *x.LikeFlag
	}
	return 0
}

func (x *FinderObject) GetLikeList() [][]byte {
	if x != nil {
		return x.LikeList
	}
	return nil
}

func (x *FinderObject) GetCommentList() []*FinderCommentInfo {
	if x != nil {
		return x.CommentList
	}
	return nil
}

func (x *FinderObject) GetForwardCount() int32 {
	if x != nil && x.ForwardCount != nil {
		return *x.ForwardCount
	}
	return 0
}

func (x *FinderObject) GetContact() *FinderContact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *FinderObject) GetDisplayidDiscarded() string {
	if x != nil && x.DisplayidDiscarded != nil {
		return *x.DisplayidDiscarded
	}
	return ""
}

func (x *FinderObject) GetRecommenderList() []*FinderRecommendInfo {
	if x != nil {
		return x.RecommenderList
	}
	return nil
}

func (x *FinderObject) GetDisplayid() uint64 {
	if x != nil && x.Displayid != nil {
		return *x.Displayid
	}
	return 0
}

func (x *FinderObject) GetLikeCount() int32 {
	if x != nil && x.LikeCount != nil {
		return *x.LikeCount
	}
	return 0
}

func (x *FinderObject) GetCommentCount() int32 {
	if x != nil && x.CommentCount != nil {
		return *x.CommentCount
	}
	return 0
}

func (x *FinderObject) GetRecommendReason() string {
	if x != nil && x.RecommendReason != nil {
		return *x.RecommendReason
	}
	return ""
}

func (x *FinderObject) GetReadCount() int32 {
	if x != nil && x.ReadCount != nil {
		return *x.ReadCount
	}
	return 0
}

func (x *FinderObject) GetDeletetime() int32 {
	if x != nil && x.Deletetime != nil {
		return *x.Deletetime
	}
	return 0
}

func (x *FinderObject) GetCommentClose() int32 {
	if x != nil && x.CommentClose != nil {
		return *x.CommentClose
	}
	return 0
}

func (x *FinderObject) GetRefObjectFlag() uint32 {
	if x != nil && x.RefObjectFlag != nil {
		return *x.RefObjectFlag
	}
	return 0
}

func (x *FinderObject) GetRefObjectid() uint32 {
	if x != nil && x.RefObjectid != nil {
		return *x.RefObjectid
	}
	return 0
}

func (x *FinderObject) GetRefObjectContact() *FinderContact {
	if x != nil {
		return x.RefObjectContact
	}
	return nil
}

func (x *FinderObject) GetRecommendType() int32 {
	if x != nil && x.RecommendType != nil {
		return *x.RecommendType
	}
	return 0
}

func (x *FinderObject) GetFriendLikeCount() int32 {
	if x != nil && x.FriendLikeCount != nil {
		return *x.FriendLikeCount
	}
	return 0
}

func (x *FinderObject) GetObjectNonceId() string {
	if x != nil && x.ObjectNonceId != nil {
		return *x.ObjectNonceId
	}
	return ""
}

func (x *FinderObject) GetRefObjectNonceId() string {
	if x != nil && x.RefObjectNonceId != nil {
		return *x.RefObjectNonceId
	}
	return ""
}

func (x *FinderObject) GetObjectStatus() int32 {
	if x != nil && x.ObjectStatus != nil {
		return *x.ObjectStatus
	}
	return 0
}

func (x *FinderObject) GetSendShareFavWording() string {
	if x != nil && x.SendShareFavWording != nil {
		return *x.SendShareFavWording
	}
	return ""
}

func (x *FinderObject) GetOriginalFlag() int32 {
	if x != nil && x.OriginalFlag != nil {
		return *x.OriginalFlag
	}
	return 0
}

func (x *FinderObject) GetSecondaryShowFlag() int32 {
	if x != nil && x.SecondaryShowFlag != nil {
		return *x.SecondaryShowFlag
	}
	return 0
}

func (x *FinderObject) GetTipsWording() string {
	if x != nil && x.TipsWording != nil {
		return *x.TipsWording
	}
	return ""
}

func (x *FinderObject) GetOrgRecommendType() int32 {
	if x != nil && x.OrgRecommendType != nil {
		return *x.OrgRecommendType
	}
	return 0
}

func (x *FinderObject) GetEnKey() string {
	if x != nil && x.EnKey != nil {
		return *x.EnKey
	}
	return ""
}

func (x *FinderObject) GetUnk38() uint64 {
	if x != nil && x.Unk38 != nil {
		return *x.Unk38
	}
	return 0
}

func (x *FinderObject) GetUnk39() uint64 {
	if x != nil && x.Unk39 != nil {
		return *x.Unk39
	}
	return 0
}

func (x *FinderObject) GetUnk43() uint64 {
	if x != nil && x.Unk43 != nil {
		return *x.Unk43
	}
	return 0
}

func (x *FinderObject) GetUnk44() uint64 {
	if x != nil && x.Unk44 != nil {
		return *x.Unk44
	}
	return 0
}

func (x *FinderObject) GetUnk57() uint64 {
	if x != nil && x.Unk57 != nil {
		return *x.Unk57
	}
	return 0
}

type FinderRecommendInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tid               *string `protobuf:"bytes,1,opt,name=tid" json:"tid,omitempty"`
	RecommendType     *uint32 `protobuf:"varint,2,opt,name=recommendType" json:"recommendType,omitempty"`
	RecommendReason   *string `protobuf:"bytes,3,opt,name=recommendReason" json:"recommendReason,omitempty"`
	OrgRecommendType  *uint32 `protobuf:"varint,4,opt,name=orgRecommendType" json:"orgRecommendType,omitempty"`
	LastInsertedRowID *uint32 `protobuf:"varint,5,opt,name=lastInsertedRowID" json:"lastInsertedRowID,omitempty"`
	IsAutoIncrement   *bool   `protobuf:"varint,6,opt,name=isAutoIncrement" json:"isAutoIncrement,omitempty"`
}

func (x *FinderRecommendInfo) Reset() {
	*x = FinderRecommendInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderRecommendInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderRecommendInfo) ProtoMessage() {}

func (x *FinderRecommendInfo) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderRecommendInfo.ProtoReflect.Descriptor instead.
func (*FinderRecommendInfo) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{4}
}

func (x *FinderRecommendInfo) GetTid() string {
	if x != nil && x.Tid != nil {
		return *x.Tid
	}
	return ""
}

func (x *FinderRecommendInfo) GetRecommendType() uint32 {
	if x != nil && x.RecommendType != nil {
		return *x.RecommendType
	}
	return 0
}

func (x *FinderRecommendInfo) GetRecommendReason() string {
	if x != nil && x.RecommendReason != nil {
		return *x.RecommendReason
	}
	return ""
}

func (x *FinderRecommendInfo) GetOrgRecommendType() uint32 {
	if x != nil && x.OrgRecommendType != nil {
		return *x.OrgRecommendType
	}
	return 0
}

func (x *FinderRecommendInfo) GetLastInsertedRowID() uint32 {
	if x != nil && x.LastInsertedRowID != nil {
		return *x.LastInsertedRowID
	}
	return 0
}

func (x *FinderRecommendInfo) GetIsAutoIncrement() bool {
	if x != nil && x.IsAutoIncrement != nil {
		return *x.IsAutoIncrement
	}
	return false
}

type FinderContact struct {
	Username     *string               `protobuf:"bytes,1,opt,name=username" json:"username,omitempty"`
	Nickname     *string               `protobuf:"bytes,2,opt,name=nickname" json:"nickname,omitempty"`
	HeadUrl      *string               `protobuf:"bytes,3,opt,name=headUrl" json:"headUrl,omitempty"`
	Seq          *uint64               `protobuf:"varint,4,opt,name=seq" json:"seq,omitempty"`
	Signature    *string               `protobuf:"bytes,5,opt,name=signature" json:"signature,omitempty"`
	FollowFlag   *int32                `protobuf:"varint,6,opt,name=followFlag" json:"followFlag,omitempty"`
	FollowTime   *int32                `protobuf:"varint,7,opt,name=followTime" json:"followTime,omitempty"`
	AuthInfo     *FinderAuthInfo       `protobuf:"bytes,8,opt,name=authInfo" json:"authInfo,omitempty"`
	CoverImgUrl  *string               `protobuf:"bytes,9,opt,name=coverImgUrl" json:"coverImgUrl,omitempty"`
	SpamStatus   *int32                `protobuf:"varint,10,opt,name=spamStatus" json:"spamStatus,omitempty"`
	ExtFlag      *int32                `protobuf:"varint,11,opt,name=extFlag" json:"extFlag,omitempty"`
	ExtInfo      *FinderContactExtInfo `protobuf:"bytes,12,opt,name=extInfo" json:"extInfo,omitempty"`
	OriginalFlag *int32                `protobuf:"varint,13,opt,name=originalFlag" json:"originalFlag,omitempty"`
}

func (x *FinderContact) Reset() {
	*x = FinderContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderContact) ProtoMessage() {}

func (x *FinderContact) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderContact.ProtoReflect.Descriptor instead.
func (*FinderContact) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{5}
}

func (x *FinderContact) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *FinderContact) GetNickname() string {
	if x != nil && x.Nickname != nil {
		return *x.Nickname
	}
	return ""
}

func (x *FinderContact) GetHeadUrl() string {
	if x != nil && x.HeadUrl != nil {
		return *x.HeadUrl
	}
	return ""
}

func (x *FinderContact) GetSeq() uint64 {
	if x != nil && x.Seq != nil {
		return *x.Seq
	}
	return 0
}

func (x *FinderContact) GetSignature() string {
	if x != nil && x.Signature != nil {
		return *x.Signature
	}
	return ""
}

func (x *FinderContact) GetFollowFlag() int32 {
	if x != nil && x.FollowFlag != nil {
		return *x.FollowFlag
	}
	return 0
}

func (x *FinderContact) GetFollowTime() int32 {
	if x != nil && x.FollowTime != nil {
		return *x.FollowTime
	}
	return 0
}

func (x *FinderContact) GetAuthInfo() *FinderAuthInfo {
	if x != nil {
		return x.AuthInfo
	}
	return nil
}

func (x *FinderContact) GetCoverImgUrl() string {
	if x != nil && x.CoverImgUrl != nil {
		return *x.CoverImgUrl
	}
	return ""
}

func (x *FinderContact) GetSpamStatus() int32 {
	if x != nil && x.SpamStatus != nil {
		return *x.SpamStatus
	}
	return 0
}

func (x *FinderContact) GetExtFlag() int32 {
	if x != nil && x.ExtFlag != nil {
		return *x.ExtFlag
	}
	return 0
}

func (x *FinderContact) GetExtInfo() *FinderContactExtInfo {
	if x != nil {
		return x.ExtInfo
	}
	return nil
}

func (x *FinderContact) GetOriginalFlag() int32 {
	if x != nil && x.OriginalFlag != nil {
		return *x.OriginalFlag
	}
	return 0
}

type FinderContactExtInfo struct {
	Country    *string `protobuf:"bytes,1,opt,name=country" json:"country,omitempty"`
	Province   *string `protobuf:"bytes,2,opt,name=province" json:"province,omitempty"`
	City       *string `protobuf:"bytes,3,opt,name=city" json:"city,omitempty"`
	Sex        *int32  `protobuf:"varint,4,opt,name=sex" json:"sex,omitempty"`
	BirthYear  *int32  `protobuf:"varint,5,opt,name=birthYear" json:"birthYear,omitempty"`
	BirthMonth *int32  `protobuf:"varint,6,opt,name=birthMonth" json:"birthMonth,omitempty"`
	BirthDay   *int32  `protobuf:"varint,7,opt,name=birthDay" json:"birthDay,omitempty"`
}

func (x *FinderContactExtInfo) Reset() {
	*x = FinderContactExtInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderContactExtInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderContactExtInfo) ProtoMessage() {}

func (x *FinderContactExtInfo) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderContactExtInfo.ProtoReflect.Descriptor instead.
func (*FinderContactExtInfo) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{6}
}

func (x *FinderContactExtInfo) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *FinderContactExtInfo) GetProvince() string {
	if x != nil && x.Province != nil {
		return *x.Province
	}
	return ""
}

func (x *FinderContactExtInfo) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *FinderContactExtInfo) GetSex() int32 {
	if x != nil && x.Sex != nil {
		return *x.Sex
	}
	return 0
}

func (x *FinderContactExtInfo) GetBirthYear() int32 {
	if x != nil && x.BirthYear != nil {
		return *x.BirthYear
	}
	return 0
}

func (x *FinderContactExtInfo) GetBirthMonth() int32 {
	if x != nil && x.BirthMonth != nil {
		return *x.BirthMonth
	}
	return 0
}

func (x *FinderContactExtInfo) GetBirthDay() int32 {
	if x != nil && x.BirthDay != nil {
		return *x.BirthDay
	}
	return 0
}

type FinderAuthInfo struct {
	// optional  string realName = 1;
	//optional  int32 authIconType = 2;
	//optional  string authProfession = 3;
	//optional  FinderContact authGuarantor = 4;
	//optional  string detailLink = 5;
	//optional  string appName = 6;
	V1           *string `protobuf:"bytes,1,opt,name=v1" json:"v1,omitempty"`
	AuthIconType *int32  `protobuf:"varint,2,opt,name=authIconType" json:"authIconType,omitempty"`
	RealName     *string `protobuf:"bytes,3,opt,name=realName" json:"realName,omitempty"`
	Photo        *string `protobuf:"bytes,7,opt,name=photo" json:"photo,omitempty"`
	V8           *int32  `protobuf:"varint,8,opt,name=v8" json:"v8,omitempty"`
	V9           *int32  `protobuf:"varint,9,opt,name=v9" json:"v9,omitempty"`
}

func (x *FinderAuthInfo) Reset() {
	*x = FinderAuthInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderAuthInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderAuthInfo) ProtoMessage() {}

func (x *FinderAuthInfo) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderAuthInfo.ProtoReflect.Descriptor instead.
func (*FinderAuthInfo) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{7}
}

func (x *FinderAuthInfo) GetV1() string {
	if x != nil && x.V1 != nil {
		return *x.V1
	}
	return ""
}

func (x *FinderAuthInfo) GetAuthIconType() int32 {
	if x != nil && x.AuthIconType != nil {
		return *x.AuthIconType
	}
	return 0
}

func (x *FinderAuthInfo) GetRealName() string {
	if x != nil && x.RealName != nil {
		return *x.RealName
	}
	return ""
}

func (x *FinderAuthInfo) GetPhoto() string {
	if x != nil && x.Photo != nil {
		return *x.Photo
	}
	return ""
}

func (x *FinderAuthInfo) GetV8() int32 {
	if x != nil && x.V8 != nil {
		return *x.V8
	}
	return 0
}

func (x *FinderAuthInfo) GetV9() int32 {
	if x != nil && x.V9 != nil {
		return *x.V9
	}
	return 0
}

type FinderObjectDesc struct {
	Description *string                `protobuf:"bytes,1,opt,name=description" json:"description,omitempty"`
	Media       []*FinderMedia         `protobuf:"bytes,2,rep,name=media" json:"media,omitempty"`
	MediaType   *int32                 `protobuf:"varint,3,opt,name=mediaType" json:"mediaType,omitempty"`
	Extra       *FinderMediaExtra      `protobuf:"bytes,4,opt,name=extra" json:"extra,omitempty"`
	Location    *FinderLocation        `protobuf:"bytes,5,opt,name=location" json:"location,omitempty"`
	ExtReading  *FinderExtendedReading `protobuf:"bytes,6,opt,name=extReading" json:"extReading,omitempty"`
	Topic       *FinderTopic           `protobuf:"bytes,7,opt,name=topic" json:"topic,omitempty"`
}

func (x *FinderObjectDesc) Reset() {
	*x = FinderObjectDesc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderObjectDesc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderObjectDesc) ProtoMessage() {}

func (x *FinderObjectDesc) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderObjectDesc.ProtoReflect.Descriptor instead.
func (*FinderObjectDesc) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{8}
}

func (x *FinderObjectDesc) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *FinderObjectDesc) GetMedia() []*FinderMedia {
	if x != nil {
		return x.Media
	}
	return nil
}

func (x *FinderObjectDesc) GetMediaType() int32 {
	if x != nil && x.MediaType != nil {
		return *x.MediaType
	}
	return 0
}

func (x *FinderObjectDesc) GetExtra() *FinderMediaExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *FinderObjectDesc) GetLocation() *FinderLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *FinderObjectDesc) GetExtReading() *FinderExtendedReading {
	if x != nil {
		return x.ExtReading
	}
	return nil
}

func (x *FinderObjectDesc) GetTopic() *FinderTopic {
	if x != nil {
		return x.Topic
	}
	return nil
}

type FinderExtendedReading struct {
	Link  *string `protobuf:"bytes,1,opt,name=link" json:"link,omitempty"`
	Title *string `protobuf:"bytes,2,opt,name=title" json:"title,omitempty"`
}

func (x *FinderExtendedReading) Reset() {
	*x = FinderExtendedReading{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderExtendedReading) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderExtendedReading) ProtoMessage() {}

func (x *FinderExtendedReading) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderExtendedReading.ProtoReflect.Descriptor instead.
func (*FinderExtendedReading) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{9}
}

func (x *FinderExtendedReading) GetLink() string {
	if x != nil && x.Link != nil {
		return *x.Link
	}
	return ""
}

func (x *FinderExtendedReading) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

type FinderTopic struct {
	FinderTopicInfo *string `protobuf:"bytes,1,opt,name=finderTopicInfo" json:"finderTopicInfo,omitempty"`
}

func (x *FinderTopic) Reset() {
	*x = FinderTopic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderTopic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderTopic) ProtoMessage() {}

func (x *FinderTopic) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderTopic.ProtoReflect.Descriptor instead.
func (*FinderTopic) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{10}
}

func (x *FinderTopic) GetFinderTopicInfo() string {
	if x != nil && x.FinderTopicInfo != nil {
		return *x.FinderTopicInfo
	}
	return ""
}

type FinderLocation struct {
	Longitude       *float32 `protobuf:"fixed32,1,opt,name=longitude" json:"longitude,omitempty"`
	Latitude        *float32 `protobuf:"fixed32,2,opt,name=latitude" json:"latitude,omitempty"`
	City            *string  `protobuf:"bytes,3,opt,name=city" json:"city,omitempty"`
	PoiName         *string  `protobuf:"bytes,4,opt,name=poiName" json:"poiName,omitempty"`
	PoiAddress      *string  `protobuf:"bytes,5,opt,name=poiAddress" json:"poiAddress,omitempty"`
	PoiClassifyId   *string  `protobuf:"bytes,6,opt,name=poiClassifyId" json:"poiClassifyId,omitempty"`
	PoiClassifyType *int32   `protobuf:"varint,7,opt,name=poiClassifyType" json:"poiClassifyType,omitempty"`
}

func (x *FinderLocation) Reset() {
	*x = FinderLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderLocation) ProtoMessage() {}

func (x *FinderLocation) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderLocation.ProtoReflect.Descriptor instead.
func (*FinderLocation) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{11}
}

func (x *FinderLocation) GetLongitude() float32 {
	if x != nil && x.Longitude != nil {
		return *x.Longitude
	}
	return 0
}

func (x *FinderLocation) GetLatitude() float32 {
	if x != nil && x.Latitude != nil {
		return *x.Latitude
	}
	return 0
}

func (x *FinderLocation) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *FinderLocation) GetPoiName() string {
	if x != nil && x.PoiName != nil {
		return *x.PoiName
	}
	return ""
}

func (x *FinderLocation) GetPoiAddress() string {
	if x != nil && x.PoiAddress != nil {
		return *x.PoiAddress
	}
	return ""
}

func (x *FinderLocation) GetPoiClassifyId() string {
	if x != nil && x.PoiClassifyId != nil {
		return *x.PoiClassifyId
	}
	return ""
}

func (x *FinderLocation) GetPoiClassifyType() int32 {
	if x != nil && x.PoiClassifyType != nil {
		return *x.PoiClassifyType
	}
	return 0
}

type FinderMediaExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text *string `protobuf:"bytes,1,opt,name=text" json:"text,omitempty"`
}

func (x *FinderMediaExtra) Reset() {
	*x = FinderMediaExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderMediaExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderMediaExtra) ProtoMessage() {}

func (x *FinderMediaExtra) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderMediaExtra.ProtoReflect.Descriptor instead.
func (*FinderMediaExtra) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{12}
}

func (x *FinderMediaExtra) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

type FinderMedia struct {
	Url                 *string            `protobuf:"bytes,1,opt,name=Url" json:"Url,omitempty"`
	ThumbUrl            *string            `protobuf:"bytes,2,opt,name=ThumbUrl" json:"ThumbUrl,omitempty"`
	MediaType           *int32             `protobuf:"varint,3,opt,name=MediaType" json:"MediaType,omitempty"`
	VideoPlayLen        *int32             `protobuf:"varint,4,opt,name=VideoPlayLen" json:"VideoPlayLen,omitempty"`
	Width               *float32           `protobuf:"fixed32,5,opt,name=Width" json:"Width,omitempty"`
	Height              *float32           `protobuf:"fixed32,6,opt,name=Height" json:"Height,omitempty"`
	Md5Sum              *string            `protobuf:"bytes,7,opt,name=Md5Sum" json:"Md5Sum,omitempty"`
	FileSize            *int32             `protobuf:"varint,8,opt,name=FileSize" json:"FileSize,omitempty"`
	Bitrate             *int32             `protobuf:"varint,9,opt,name=Bitrate" json:"Bitrate,omitempty"`
	Spec                []*FinderMediaSpec `protobuf:"bytes,10,rep,name=Spec" json:"Spec,omitempty"`
	FinderMediaV11      *string            `protobuf:"bytes,11,opt,name=FinderMediaV11" json:"FinderMediaV11,omitempty"`
	FinderMediaV12      *string            `protobuf:"bytes,12,opt,name=FinderMediaV12" json:"FinderMediaV12,omitempty"`
	FinderMediaV13Token *string            `protobuf:"bytes,13,opt,name=FinderMediaV13Token" json:"FinderMediaV13Token,omitempty"`
	FinderMediaV14Token *string            `protobuf:"bytes,14,opt,name=FinderMediaV14Token" json:"FinderMediaV14Token,omitempty"`
	FinderMediaV21      *string            `protobuf:"bytes,21,opt,name=FinderMediaV21" json:"FinderMediaV21,omitempty"`
	FinderMediaV22      *string            `protobuf:"bytes,22,opt,name=FinderMediaV22" json:"FinderMediaV22,omitempty"`
}

func (x *FinderMedia) Reset() {
	*x = FinderMedia{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderMedia) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderMedia) ProtoMessage() {}

func (x *FinderMedia) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderMedia.ProtoReflect.Descriptor instead.
func (*FinderMedia) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{13}
}

func (x *FinderMedia) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *FinderMedia) GetThumbUrl() string {
	if x != nil && x.ThumbUrl != nil {
		return *x.ThumbUrl
	}
	return ""
}

func (x *FinderMedia) GetMediaType() int32 {
	if x != nil && x.MediaType != nil {
		return *x.MediaType
	}
	return 0
}

func (x *FinderMedia) GetVideoPlayLen() int32 {
	if x != nil && x.VideoPlayLen != nil {
		return *x.VideoPlayLen
	}
	return 0
}

func (x *FinderMedia) GetWidth() float32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *FinderMedia) GetHeight() float32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *FinderMedia) GetMd5Sum() string {
	if x != nil && x.Md5Sum != nil {
		return *x.Md5Sum
	}
	return ""
}

func (x *FinderMedia) GetFileSize() int32 {
	if x != nil && x.FileSize != nil {
		return *x.FileSize
	}
	return 0
}

func (x *FinderMedia) GetBitrate() int32 {
	if x != nil && x.Bitrate != nil {
		return *x.Bitrate
	}
	return 0
}

func (x *FinderMedia) GetSpec() []*FinderMediaSpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

func (x *FinderMedia) GetFinderMediaV11() string {
	if x != nil && x.FinderMediaV11 != nil {
		return *x.FinderMediaV11
	}
	return ""
}

func (x *FinderMedia) GetFinderMediaV12() string {
	if x != nil && x.FinderMediaV12 != nil {
		return *x.FinderMediaV12
	}
	return ""
}

func (x *FinderMedia) GetFinderMediaV13Token() string {
	if x != nil && x.FinderMediaV13Token != nil {
		return *x.FinderMediaV13Token
	}
	return ""
}

func (x *FinderMedia) GetFinderMediaV14Token() string {
	if x != nil && x.FinderMediaV14Token != nil {
		return *x.FinderMediaV14Token
	}
	return ""
}

func (x *FinderMedia) GetFinderMediaV21() string {
	if x != nil && x.FinderMediaV21 != nil {
		return *x.FinderMediaV21
	}
	return ""
}

func (x *FinderMedia) GetFinderMediaV22() string {
	if x != nil && x.FinderMediaV22 != nil {
		return *x.FinderMediaV22
	}
	return ""
}

type FinderMediaSpec struct {
	FileFormat     *string `protobuf:"bytes,1,opt,name=fileFormat" json:"fileFormat,omitempty"`
	FirstLoadBytes *int32  `protobuf:"varint,2,opt,name=firstLoadBytes" json:"firstLoadBytes,omitempty"`
	BitRate        *int32  `protobuf:"varint,3,opt,name=bitRate" json:"bitRate,omitempty"`
	CodingFormat   *string `protobuf:"bytes,4,opt,name=codingFormat" json:"codingFormat,omitempty"`
}

func (x *FinderMediaSpec) Reset() {
	*x = FinderMediaSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderGetCommentDetail_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderMediaSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderMediaSpec) ProtoMessage() {}

func (x *FinderMediaSpec) ProtoReflect() protoreflect.Message {
	mi := &file_FinderGetCommentDetail_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderMediaSpec.ProtoReflect.Descriptor instead.
func (*FinderMediaSpec) Descriptor() ([]byte, []int) {
	return file_FinderGetCommentDetail_proto_rawDescGZIP(), []int{14}
}

func (x *FinderMediaSpec) GetFileFormat() string {
	if x != nil && x.FileFormat != nil {
		return *x.FileFormat
	}
	return ""
}

func (x *FinderMediaSpec) GetFirstLoadBytes() int32 {
	if x != nil && x.FirstLoadBytes != nil {
		return *x.FirstLoadBytes
	}
	return 0
}

func (x *FinderMediaSpec) GetBitRate() int32 {
	if x != nil && x.BitRate != nil {
		return *x.BitRate
	}
	return 0
}

func (x *FinderMediaSpec) GetCodingFormat() string {
	if x != nil && x.CodingFormat != nil {
		return *x.CodingFormat
	}
	return ""
}

var File_FinderGetCommentDetail_proto protoreflect.FileDescriptor

var file_FinderGetCommentDetail_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02,
	0x67, 0x67, 0x22, 0xff, 0x01, 0x0a, 0x19, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70,
	0x12, 0x2a, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x06,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x67,
	0x67, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x12,
	0x0a, 0x0a, 0x4c, 0x61, 0x73, 0x74, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0c, 0x12, 0x14, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x12, 0x16, 0x0a, 0x0e, 0x55, 0x70, 0x43, 0x6f,
	0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x12, 0x18, 0x0a, 0x10, 0x44, 0x6f, 0x77, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65,
	0x46, 0x6c, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x12, 0x1d, 0x0a, 0x15, 0x4e, 0x65,
	0x78, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x12, 0x19, 0x0a, 0x11, 0x53, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x53, 0x68, 0x6f, 0x77, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0d, 0x22, 0x8e, 0x04, 0x0a, 0x11, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0f,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x11, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x12, 0x16, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x12, 0x12, 0x0a, 0x0a, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0f,
	0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x2c, 0x0a, 0x0f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x54, 0x77, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x67, 0x2e, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x54, 0x77, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0d, 0x12, 0x15, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x12, 0x1a, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x61, 0x72, 0x64, 0x65, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x6c, 0x69, 0x6b, 0x65, 0x46, 0x6c, 0x61, 0x67,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x12, 0x11, 0x0a, 0x09, 0x6c, 0x69, 0x6b, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x12, 0x11, 0x0a, 0x09, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x12, 0x1a, 0x0a, 0x12,
	0x65, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x12, 0x12, 0x0a, 0x0a, 0x6c, 0x61, 0x73, 0x74,
	0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0c, 0x12, 0x14, 0x0a, 0x0c,
	0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x05, 0x12, 0x13, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x12, 0x15, 0x0a, 0x0d, 0x62, 0x6c, 0x61, 0x63, 0x6b,
	0x6c, 0x69, 0x73, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x12, 0x14,
	0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x15, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x12, 0x16, 0x0a,
	0x0e, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x05, 0x22, 0xcc, 0x01, 0x0a, 0x0f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x54,
	0x77, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x0a, 0x0a, 0x02, 0x76, 0x31, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0a, 0x0a, 0x02, 0x76, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x0a, 0x0a, 0x02, 0x76, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0a, 0x0a,
	0x02, 0x76, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0a, 0x0a, 0x02, 0x76, 0x35, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0a, 0x0a, 0x02, 0x76, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x04, 0x12, 0x0a, 0x0a, 0x02, 0x76, 0x37, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0a, 0x0a,
	0x02, 0x76, 0x39, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0b, 0x0a, 0x03, 0x76, 0x31, 0x30,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03, 0x76, 0x31, 0x32, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x04, 0x12, 0x0b, 0x0a, 0x03, 0x76, 0x31, 0x33, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04,
	0x12, 0x0b, 0x0a, 0x03, 0x76, 0x31, 0x34, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0b, 0x0a,
	0x03, 0x76, 0x31, 0x38, 0x18, 0x12, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0b, 0x0a, 0x03, 0x76, 0x32,
	0x30, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03, 0x76, 0x32, 0x31, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x22, 0x8d, 0x07, 0x0a, 0x0c, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x12, 0x10, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x28, 0x0a, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x44,
	0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x67, 0x2e, 0x46,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x65, 0x73, 0x63, 0x12,
	0x12, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x12, 0x10, 0x0a, 0x08, 0x6c, 0x69, 0x6b, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x12, 0x10, 0x0a, 0x08, 0x6c, 0x69, 0x6b, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0c, 0x12, 0x2a, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67,
	0x67, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x0c, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x12, 0x22, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x67, 0x2e,
	0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x1a, 0x0a,
	0x12, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x61, 0x72,
	0x64, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x12, 0x30, 0x0a, 0x0f, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x11, 0x0a, 0x09, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x12, 0x11,
	0x0a, 0x09, 0x6c, 0x69, 0x6b, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x05, 0x12, 0x14, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x12, 0x17, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x11, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x05, 0x12, 0x12, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x12, 0x14, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x12, 0x15, 0x0a,
	0x0d, 0x72, 0x65, 0x66, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x0d, 0x12, 0x13, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x12, 0x2b, 0x0a, 0x10, 0x72, 0x65, 0x66,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x15, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x12, 0x17, 0x0a,
	0x0f, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x4c, 0x69, 0x6b, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x12, 0x15, 0x0a, 0x0d, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x12, 0x18, 0x0a,
	0x10, 0x72, 0x65, 0x66, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x12, 0x14, 0x0a, 0x0c, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x12, 0x1b, 0x0a,
	0x13, 0x73, 0x65, 0x6e, 0x64, 0x53, 0x68, 0x61, 0x72, 0x65, 0x46, 0x61, 0x76, 0x57, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x12, 0x14, 0x0a, 0x0c, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x05,
	0x12, 0x19, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x53, 0x68, 0x6f,
	0x77, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x05, 0x12, 0x13, 0x0a, 0x0b, 0x74,
	0x69, 0x70, 0x73, 0x57, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x18, 0x0a, 0x10, 0x6f, 0x72, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0d, 0x0a, 0x05, 0x65, 0x6e,
	0x4b, 0x65, 0x79, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0d, 0x0a, 0x05, 0x75, 0x6e, 0x6b,
	0x33, 0x38, 0x18, 0x26, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0d, 0x0a, 0x05, 0x75, 0x6e, 0x6b, 0x33,
	0x39, 0x18, 0x27, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0d, 0x0a, 0x05, 0x75, 0x6e, 0x6b, 0x34, 0x33,
	0x18, 0x2b, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0d, 0x0a, 0x05, 0x75, 0x6e, 0x6b, 0x34, 0x34, 0x18,
	0x2c, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0d, 0x0a, 0x05, 0x75, 0x6e, 0x6b, 0x35, 0x37, 0x18, 0x39,
	0x20, 0x01, 0x28, 0x04, 0x22, 0xa0, 0x01, 0x0a, 0x13, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0b, 0x0a, 0x03,
	0x74, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x15, 0x0a, 0x0d, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x12, 0x17, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x18, 0x0a, 0x10, 0x6f, 0x72, 0x67,
	0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x12, 0x19, 0x0a, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x65, 0x72,
	0x74, 0x65, 0x64, 0x52, 0x6f, 0x77, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x12, 0x17,
	0x0a, 0x0f, 0x69, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x22, 0xad, 0x02, 0x0a, 0x0d, 0x46, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x10, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x6e,
	0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0f, 0x0a,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b,
	0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x12, 0x11, 0x0a, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x12, 0x12,
	0x0a, 0x0a, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x12, 0x12, 0x0a, 0x0a, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x67, 0x2e, 0x46, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x13, 0x0a, 0x0b,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x12, 0x0a, 0x0a, 0x73, 0x70, 0x61, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0f, 0x0a, 0x07, 0x65, 0x78, 0x74, 0x46, 0x6c, 0x61, 0x67,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x12, 0x29, 0x0a, 0x07, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x67, 0x2e, 0x46, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x14, 0x0a, 0x0c, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x22, 0x8d, 0x01, 0x0a, 0x14, 0x46, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0f, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x10, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x0b, 0x0a, 0x03, 0x73, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x12, 0x11,
	0x0a, 0x09, 0x62, 0x69, 0x72, 0x74, 0x68, 0x59, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x12, 0x12, 0x0a, 0x0a, 0x62, 0x69, 0x72, 0x74, 0x68, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x12, 0x10, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x44, 0x61,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x22, 0x6b, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0a, 0x0a, 0x02, 0x76, 0x31, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x14, 0x0a, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x12, 0x10, 0x0a, 0x08, 0x72,
	0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0d, 0x0a,
	0x05, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0a, 0x0a, 0x02,
	0x76, 0x38, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0a, 0x0a, 0x02, 0x76, 0x39, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x22, 0xf4, 0x01, 0x0a, 0x10, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x65, 0x73, 0x63, 0x12, 0x13, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x1e,
	0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x67, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x11,
	0x0a, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x67, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x24, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x67, 0x2e, 0x46, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x0a,
	0x65, 0x78, 0x74, 0x52, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x52, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x0a, 0x05, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x67, 0x2e,
	0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x22, 0x34, 0x0a, 0x15, 0x46,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x52, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x0c, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x12, 0x0d, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x22, 0x26, 0x0a, 0x0b, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x12, 0x17, 0x0a, 0x0f, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x22, 0x98, 0x01, 0x0a, 0x0e, 0x46, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x11, 0x0a, 0x09,
	0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x12,
	0x10, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x12, 0x0c, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x0f, 0x0a, 0x07, 0x70, 0x6f, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x12, 0x0a, 0x0a, 0x70, 0x6f, 0x69, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x15, 0x0a, 0x0d, 0x70, 0x6f, 0x69, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x69, 0x66, 0x79, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x12, 0x17, 0x0a, 0x0f, 0x70,
	0x6f, 0x69, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x22, 0x20, 0x0a, 0x10, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x0c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x22, 0xe4, 0x02, 0x0a, 0x0b, 0x46, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x0b, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x54, 0x68, 0x75, 0x6d, 0x62, 0x55, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x11, 0x0a, 0x09, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x12, 0x14, 0x0a, 0x0c, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x50, 0x6c, 0x61, 0x79, 0x4c, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0d,
	0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x12, 0x0e, 0x0a,
	0x06, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x12, 0x0e, 0x0a,
	0x06, 0x4d, 0x64, 0x35, 0x53, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10, 0x0a,
	0x08, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x12,
	0x0f, 0x0a, 0x07, 0x42, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05,
	0x12, 0x21, 0x0a, 0x04, 0x53, 0x70, 0x65, 0x63, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x67, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x53,
	0x70, 0x65, 0x63, 0x12, 0x16, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x56, 0x31, 0x31, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x12, 0x16, 0x0a, 0x0e, 0x46,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x56, 0x31, 0x32, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x12, 0x1b, 0x0a, 0x13, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x56, 0x31, 0x33, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x1b, 0x0a, 0x13, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x56,
	0x31, 0x34, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x12, 0x16, 0x0a,
	0x0e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x56, 0x32, 0x31, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x12, 0x16, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x56, 0x32, 0x32, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x22, 0x64, 0x0a,
	0x0f, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x70, 0x65, 0x63,
	0x12, 0x12, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x16, 0x0a, 0x0e, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4c, 0x6f, 0x61,
	0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0f, 0x0a, 0x07,
	0x62, 0x69, 0x74, 0x52, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x12, 0x14, 0x0a,
	0x0c, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09,
}

var (
	file_FinderGetCommentDetail_proto_rawDescOnce sync.Once
	file_FinderGetCommentDetail_proto_rawDescData = file_FinderGetCommentDetail_proto_rawDesc
)

func file_FinderGetCommentDetail_proto_rawDescGZIP() []byte {
	file_FinderGetCommentDetail_proto_rawDescOnce.Do(func() {
		file_FinderGetCommentDetail_proto_rawDescData = protoimpl.X.CompressGZIP(file_FinderGetCommentDetail_proto_rawDescData)
	})
	return file_FinderGetCommentDetail_proto_rawDescData
}

var file_FinderGetCommentDetail_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_FinderGetCommentDetail_proto_goTypes = []interface{}{
	(*FinderGetCommentDetailRsp)(nil), // 0: gg.FinderGetCommentDetailRsp
	(*FinderCommentInfo)(nil),         // 1: gg.FinderCommentInfo
	(*LevelTwoComment)(nil),           // 2: gg.LevelTwoComment
	(*FinderObject)(nil),              // 3: gg.FinderObject
	(*FinderRecommendInfo)(nil),       // 4: gg.FinderRecommendInfo
	(*FinderContact)(nil),             // 5: gg.FinderContact
	(*FinderContactExtInfo)(nil),      // 6: gg.FinderContactExtInfo
	(*FinderAuthInfo)(nil),            // 7: gg.FinderAuthInfo
	(*FinderObjectDesc)(nil),          // 8: gg.FinderObjectDesc
	(*FinderExtendedReading)(nil),     // 9: gg.FinderExtendedReading
	(*FinderTopic)(nil),               // 10: gg.FinderTopic
	(*FinderLocation)(nil),            // 11: gg.FinderLocation
	(*FinderMediaExtra)(nil),          // 12: gg.FinderMediaExtra
	(*FinderMedia)(nil),               // 13: gg.FinderMedia
	(*FinderMediaSpec)(nil),           // 14: gg.FinderMediaSpec
}
var file_FinderGetCommentDetail_proto_depIdxs = []int32{
	1,  // 0: gg.FinderGetCommentDetailRsp.CommentInfo:type_name -> gg.FinderCommentInfo
	3,  // 1: gg.FinderGetCommentDetailRsp.Object:type_name -> gg.FinderObject
	2,  // 2: gg.FinderCommentInfo.levelTwoComment:type_name -> gg.LevelTwoComment
	8,  // 3: gg.FinderObject.objectDesc:type_name -> gg.FinderObjectDesc
	1,  // 4: gg.FinderObject.commentList:type_name -> gg.FinderCommentInfo
	5,  // 5: gg.FinderObject.contact:type_name -> gg.FinderContact
	4,  // 6: gg.FinderObject.recommenderList:type_name -> gg.FinderRecommendInfo
	5,  // 7: gg.FinderObject.refObjectContact:type_name -> gg.FinderContact
	7,  // 8: gg.FinderContact.authInfo:type_name -> gg.FinderAuthInfo
	6,  // 9: gg.FinderContact.extInfo:type_name -> gg.FinderContactExtInfo
	13, // 10: gg.FinderObjectDesc.media:type_name -> gg.FinderMedia
	12, // 11: gg.FinderObjectDesc.extra:type_name -> gg.FinderMediaExtra
	11, // 12: gg.FinderObjectDesc.location:type_name -> gg.FinderLocation
	9,  // 13: gg.FinderObjectDesc.extReading:type_name -> gg.FinderExtendedReading
	10, // 14: gg.FinderObjectDesc.topic:type_name -> gg.FinderTopic
	14, // 15: gg.FinderMedia.Spec:type_name -> gg.FinderMediaSpec
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_FinderGetCommentDetail_proto_init() }
func file_FinderGetCommentDetail_proto_init() {
	if File_FinderGetCommentDetail_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_FinderGetCommentDetail_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinderRecommendInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_FinderGetCommentDetail_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinderMediaExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_FinderGetCommentDetail_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_FinderGetCommentDetail_proto_goTypes,
		DependencyIndexes: file_FinderGetCommentDetail_proto_depIdxs,
		MessageInfos:      file_FinderGetCommentDetail_proto_msgTypes,
	}.Build()
	File_FinderGetCommentDetail_proto = out.File
	file_FinderGetCommentDetail_proto_rawDesc = nil
	file_FinderGetCommentDetail_proto_goTypes = nil
	file_FinderGetCommentDetail_proto_depIdxs = nil
}
