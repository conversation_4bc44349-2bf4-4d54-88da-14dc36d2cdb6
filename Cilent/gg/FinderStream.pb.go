// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        (unknown)
// source: FinderStream.proto

package gg

import (
	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type BaseRsp struct {
	Code *uint64 `protobuf:"varint,1,opt,name=code" json:"code,omitempty"`
	Ret  *ByBing `protobuf:"bytes,2,opt,name=ret" json:"ret,omitempty"`
}

func (x *BaseRsp) Reset() {
	*x = BaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderStream_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseRsp) ProtoMessage() {}

func (x *BaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_FinderStream_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseRsp.ProtoReflect.Descriptor instead.
func (*BaseRsp) Descriptor() ([]byte, []int) {
	return file_FinderStream_proto_rawDescGZIP(), []int{0}
}

func (x *BaseRsp) GetCode() uint64 {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return 0
}

func (x *BaseRsp) GetRet() *ByBing {
	if x != nil {
		return x.Ret
	}
	return nil
}

type ByBing struct {
	Ret *string `protobuf:"bytes,2,opt,name=ret" json:"ret,omitempty"`
}

func (x *ByBing) Reset() {
	*x = ByBing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderStream_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ByBing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ByBing) ProtoMessage() {}

func (x *ByBing) ProtoReflect() protoreflect.Message {
	mi := &file_FinderStream_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ByBing.ProtoReflect.Descriptor instead.
func (*ByBing) Descriptor() ([]byte, []int) {
	return file_FinderStream_proto_rawDescGZIP(), []int{1}
}

func (x *ByBing) GetRet() string {
	if x != nil && x.Ret != nil {
		return *x.Ret
	}
	return ""
}

type FinderStreamResponse struct {
	//BaseRsp      *BaseRsp        `protobuf:"bytes,1,opt,name=baseRsp" json:"baseRsp,omitempty"`
	FinderObject []*FinderObject `protobuf:"bytes,2,rep,name=finderObject" json:"finderObject,omitempty"`
	//Text         *string         `protobuf:"bytes,22,opt,name=text" json:"text,omitempty"`
}

func (x *FinderStreamResponse) Reset() {
	*x = FinderStreamResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_FinderStream_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderStreamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderStreamResponse) ProtoMessage() {}

func (x *FinderStreamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_FinderStream_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderStreamResponse.ProtoReflect.Descriptor instead.
func (*FinderStreamResponse) Descriptor() ([]byte, []int) {
	return file_FinderStream_proto_rawDescGZIP(), []int{2}
}

func (x *FinderStreamResponse) GetBaseRsp() *BaseRsp {
	if x != nil {
		return nil // x.BaseRsp
	}
	return nil
}

func (x *FinderStreamResponse) GetFinderObject() []*FinderObject {
	if x != nil {
		return x.FinderObject
	}
	return nil
}

var File_FinderStream_proto protoreflect.FileDescriptor

var file_FinderStream_proto_rawDesc = []byte{
	0x0a, 0x12, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x67, 0x67, 0x1a, 0x1c, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x30, 0x0a, 0x07, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x0c, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x12,
	0x17, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x67,
	0x67, 0x2e, 0x42, 0x79, 0x42, 0x69, 0x6e, 0x67, 0x22, 0x15, 0x0a, 0x06, 0x42, 0x79, 0x42, 0x69,
	0x6e, 0x67, 0x12, 0x0b, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x22,
	0x6a, 0x0a, 0x14, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x07, 0x62, 0x61, 0x73, 0x65, 0x52,
	0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x67, 0x67, 0x2e, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x67, 0x67,
	0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0c, 0x0a,
	0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09,
}

var (
	file_FinderStream_proto_rawDescOnce sync.Once
	file_FinderStream_proto_rawDescData = file_FinderStream_proto_rawDesc
)

func file_FinderStream_proto_rawDescGZIP() []byte {
	file_FinderStream_proto_rawDescOnce.Do(func() {
		file_FinderStream_proto_rawDescData = protoimpl.X.CompressGZIP(file_FinderStream_proto_rawDescData)
	})
	return file_FinderStream_proto_rawDescData
}

var file_FinderStream_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_FinderStream_proto_goTypes = []interface{}{
	(*BaseRsp)(nil),              // 0: gg.BaseRsp
	(*ByBing)(nil),               // 1: gg.ByBing
	(*FinderStreamResponse)(nil), // 2: gg.FinderStreamResponse
	(*FinderObject)(nil),         // 3: gg.FinderObject
}
var file_FinderStream_proto_depIdxs = []int32{
	1, // 0: gg.BaseRsp.ret:type_name -> gg.ByBing
	0, // 1: gg.FinderStreamResponse.baseRsp:type_name -> gg.BaseRsp
	3, // 2: gg.FinderStreamResponse.finderObject:type_name -> gg.FinderObject
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_FinderStream_proto_init() }
func file_FinderStream_proto_init() {
	if File_FinderStream_proto != nil {
		return
	}
	file_FinderGetCommentDetail_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_FinderStream_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_FinderStream_proto_goTypes,
		DependencyIndexes: file_FinderStream_proto_depIdxs,
		MessageInfos:      file_FinderStream_proto_msgTypes,
	}.Build()
	File_FinderStream_proto = out.File
	file_FinderStream_proto_rawDesc = nil
	file_FinderStream_proto_goTypes = nil
	file_FinderStream_proto_depIdxs = nil
}
