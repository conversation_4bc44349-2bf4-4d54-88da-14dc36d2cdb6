syntax="proto2";

import "mm.proto";
package mm;
option go_package = "../mm";

message FinderGetMsgSessionIdRequest{
  optional uint64 myAccountType=2;
  optional string finderUsername=3;
  optional FinderBaseRequest finderBaseRequest=4;
}

message RespV{
  optional int32 ret=1;
}
message Response{
  optional int32 ret=1;
  optional SKBuiltinString_ts sk=2;
}

message FinderGetMsgSessionIdResponse{
  optional RespV resp=1;
  optional string finderMsg=2;
  optional uint64 enableAction=3;
  optional string finderUsername=4;
  optional FinderMsgSessionInfo finderMsgSessionInfo=5;
}
message FinderMsgSessionInfo{
  optional string finderUsername=1;
  optional string sessionId=2;
  optional uint64 rejectMsg=3;
  optional uint64 enableAction=4;
}
message FinderGetContactListReq{
  optional FinderBaseRequest finderBaseRequest=2;
  optional string finderUsername=3;
  optional uint64 contactType=4;
}
message FinderGetContactListResp{
  optional RespV resp=1;
}
message ContactList{
  optional string finderUsername=1;
  optional string nickName=2;
  optional string desc=3;
  optional uint64 listV6=6;
  optional string imgBuf=9;
  optional uint64 bitMask=10;
  optional uint64 bitVal=11;
  optional Region region=12;
  optional uint64 v15=15;
  optional uint64 v17=17;
  optional string v18=18;
}
message Region{
  optional string region=1;
  optional string province=2;
  optional string city=3;
  optional uint64 reg4=4;
}
message BypSendRequest{
  optional BaseRequest baseRequest= 1;
  optional uint64 bizType=2;
  optional BypMsgPack bypMsgPack=3;
}
message BypSendResponse{
  optional Response response=1;
  optional uint64 msgId=2;
}

message MsgExtInfo{
  repeated EInfo info=1;
  optional uint64 infoV2=2;
}
message EInfo{
  optional uint64 einfoV1=1;
  optional uint64 einfoV2=2;
}
message BypMsgPack{
  optional uint64 msgType=1;
  optional string fromUsername=2;
  optional string toUsername=3;
  optional string msgSessionId=4;
  optional string msgSource=5;
  optional string v6=6;
  optional string sessionId=7;
  optional SKBuiltinString_ts sk=101;
  optional BypMsgImgPack skImg=102;
  optional BypMsgVideoPack skVideo=103;
}
message BypMsgVideoPack{
  optional string bypVideoV1=1;
  optional string bypVideoV2=2;
  optional uint64 bypVideoV3=3;
  optional uint64 bypVideoV4=4;
  optional uint64 bypVideoV5=5;
  optional string bypVideoV6=6;
  optional string bypVideoV7=7;
  optional uint64 bypVideoV8=8;
  optional uint64 bypVideoV9=9;
  optional uint64 bypVideoV10=10;
  optional string bypVideoV11=11;
  optional string bypVideoV12=12;
  optional string bypVideoV13=13;
}
message BypMsgImgPack{
  optional string bypV1=1;
  optional string fromUsername=2;
  optional string bypV3=3;
  optional uint64 bypV4=4;
  optional uint64 bypV5=5;
  optional uint64 bypV6=6;
  optional uint64 bypV7=7;
  optional uint64 bypV8=8;
  optional string bypV9=9;
  optional uint64 bypV15=15;
}
message BypSyncRequest{
  optional  BaseRequest  baseRequest  = 1;
  optional uint64 scene=2;
  optional MsgExtInfo msgExtInfo=3;
  optional uint64 uExt=4;
}
message BypSyncResponse{
  optional Response response=1;
  optional MsgExtInfo msgExtInfo=3;
  optional SyncItemList syncItemList=4;
}
message SyncItemList{
  optional uint64 syncItemV1=1;
  optional uint64 syncItemV2=2;
  optional SyncItem syncItem=3;
}
message SyncItem{
  optional FinderSync finderSync=1;
  optional string fromUsername=2;
  optional string SyncItemV3=3;
  optional EInfo syncInfo=4;
  optional uint64 syncV5=5;
}
message FinderSync{
  optional uint64 finderSyncV1=1;
  optional SKBuiltinString_ts finderSyncV2=2;
  optional SKBuiltinString_ts finderSyncV3=3;
  optional uint64 finderSyncV4=4;
  optional SKBuiltinString_ts finderSyncV5=5;
  optional uint64 finderSyncV6=6;
  optional uint64 finderSyncV7=7;
  optional uint64 finderSyncV9=9;
  optional string finderSyncV10=10;
  optional uint64 finderSyncV12=12;
  optional uint64 finderSyncV13=13;
}

message SKBuiltinString_ts {
  optional  string  string  = 1;
}
