// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.10.0
// source: finderSearchList.proto

package mm

import (
	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type FinderSearchGetHotWordListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRequest       *BaseRequest       `protobuf:"bytes,1,opt,name=baseRequest" json:"baseRequest,omitempty"`
	FinderBaseRequest *FinderBaseRequest `protobuf:"bytes,2,opt,name=finderBaseRequest" json:"finderBaseRequest,omitempty"`
}

func (x *FinderSearchGetHotWordListRequest) Reset() {
	*x = FinderSearchGetHotWordListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderSearchList_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderSearchGetHotWordListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderSearchGetHotWordListRequest) ProtoMessage() {}

func (x *FinderSearchGetHotWordListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_finderSearchList_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderSearchGetHotWordListRequest.ProtoReflect.Descriptor instead.
func (*FinderSearchGetHotWordListRequest) Descriptor() ([]byte, []int) {
	return file_finderSearchList_proto_rawDescGZIP(), []int{0}
}

func (x *FinderSearchGetHotWordListRequest) GetBaseRequest() *BaseRequest {
	if x != nil {
		return x.BaseRequest
	}
	return nil
}

func (x *FinderSearchGetHotWordListRequest) GetFinderBaseRequest() *FinderBaseRequest {
	if x != nil {
		return x.FinderBaseRequest
	}
	return nil
}

type FinderSearchGetHotWordListResponse struct {
	BaseResponse               *Resp                         `protobuf:"bytes,1,opt,name=baseResponse" json:"baseResponse,omitempty"`
	FinderSearchGetHotWordList []*FinderSearchGetHotWordList `protobuf:"bytes,2,rep,name=finderSearchGetHotWordList" json:"finderSearchGetHotWordList,omitempty"`
}

func (x *FinderSearchGetHotWordListResponse) Reset() {
	*x = FinderSearchGetHotWordListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderSearchList_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderSearchGetHotWordListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderSearchGetHotWordListResponse) ProtoMessage() {}

func (x *FinderSearchGetHotWordListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_finderSearchList_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderSearchGetHotWordListResponse.ProtoReflect.Descriptor instead.
func (*FinderSearchGetHotWordListResponse) Descriptor() ([]byte, []int) {
	return file_finderSearchList_proto_rawDescGZIP(), []int{1}
}

func (x *FinderSearchGetHotWordListResponse) GetBaseResponse() *Resp {
	if x != nil {
		return x.BaseResponse
	}
	return nil
}

func (x *FinderSearchGetHotWordListResponse) GetFinderSearchGetHotWordList() []*FinderSearchGetHotWordList {
	if x != nil {
		return x.FinderSearchGetHotWordList
	}
	return nil
}

type Resp struct {
	Ret *int32 `protobuf:"varint,1,opt,name=ret" json:"ret,omitempty"`
}

func (x *Resp) Reset() {
	*x = Resp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderSearchList_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resp) ProtoMessage() {}

func (x *Resp) ProtoReflect() protoreflect.Message {
	mi := &file_finderSearchList_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resp.ProtoReflect.Descriptor instead.
func (*Resp) Descriptor() ([]byte, []int) {
	return file_finderSearchList_proto_rawDescGZIP(), []int{2}
}

func (x *Resp) GetRet() int32 {
	if x != nil && x.Ret != nil {
		return *x.Ret
	}
	return 0
}

type FinderSearchGetHotWordList struct {
	Title  *string             `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`
	Number *int32              `protobuf:"varint,2,opt,name=number" json:"number,omitempty"`
	Data   *FinderSearchIdList `protobuf:"bytes,5,opt,name=data" json:"data,omitempty"`
}

func (x *FinderSearchGetHotWordList) Reset() {
	*x = FinderSearchGetHotWordList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderSearchList_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderSearchGetHotWordList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderSearchGetHotWordList) ProtoMessage() {}

func (x *FinderSearchGetHotWordList) ProtoReflect() protoreflect.Message {
	mi := &file_finderSearchList_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderSearchGetHotWordList.ProtoReflect.Descriptor instead.
func (*FinderSearchGetHotWordList) Descriptor() ([]byte, []int) {
	return file_finderSearchList_proto_rawDescGZIP(), []int{3}
}

func (x *FinderSearchGetHotWordList) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *FinderSearchGetHotWordList) GetNumber() int32 {
	if x != nil && x.Number != nil {
		return *x.Number
	}
	return 0
}

func (x *FinderSearchGetHotWordList) GetData() *FinderSearchIdList {
	if x != nil {
		return x.Data
	}
	return nil
}

type FinderSearchIdList struct {
	Id     *uint64 `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Number *uint32 `protobuf:"varint,2,opt,name=number" json:"number,omitempty"`
}

func (x *FinderSearchIdList) Reset() {
	*x = FinderSearchIdList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderSearchList_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderSearchIdList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderSearchIdList) ProtoMessage() {}

func (x *FinderSearchIdList) ProtoReflect() protoreflect.Message {
	mi := &file_finderSearchList_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderSearchIdList.ProtoReflect.Descriptor instead.
func (*FinderSearchIdList) Descriptor() ([]byte, []int) {
	return file_finderSearchList_proto_rawDescGZIP(), []int{4}
}

func (x *FinderSearchIdList) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *FinderSearchIdList) GetNumber() uint32 {
	if x != nil && x.Number != nil {
		return *x.Number
	}
	return 0
}

var File_finderSearchList_proto protoreflect.FileDescriptor

var file_finderSearchList_proto_rawDesc = []byte{
	0x0a, 0x16, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x6d, 0x6d, 0x1a, 0x08, 0x6d, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x95, 0x01, 0x0a, 0x21, 0x46, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x74, 0x57, 0x6f, 0x72,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0b,
	0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0b, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x11,
	0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x11, 0x66, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xb2,
	0x01, 0x0a, 0x22, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x47,
	0x65, 0x74, 0x48, 0x6f, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x6d, 0x6d,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x1a, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x6d, 0x2e, 0x46, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x74, 0x57,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x1a, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x18, 0x0a, 0x04, 0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x72,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x72, 0x65, 0x74, 0x22, 0x76, 0x0a,
	0x1a, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x48, 0x6f, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x6d, 0x2e, 0x46, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3c, 0x0a, 0x12, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x42, 0x07, 0x5a, 0x05, 0x2e, 0x2e, 0x2f, 0x6d, 0x6d,
}

var (
	file_finderSearchList_proto_rawDescOnce sync.Once
	file_finderSearchList_proto_rawDescData = file_finderSearchList_proto_rawDesc
)

func file_finderSearchList_proto_rawDescGZIP() []byte {
	file_finderSearchList_proto_rawDescOnce.Do(func() {
		file_finderSearchList_proto_rawDescData = protoimpl.X.CompressGZIP(file_finderSearchList_proto_rawDescData)
	})
	return file_finderSearchList_proto_rawDescData
}

var file_finderSearchList_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_finderSearchList_proto_goTypes = []interface{}{
	(*FinderSearchGetHotWordListRequest)(nil),  // 0: mm.FinderSearchGetHotWordListRequest
	(*FinderSearchGetHotWordListResponse)(nil), // 1: mm.FinderSearchGetHotWordListResponse
	(*Resp)(nil),                       // 2: mm.Resp
	(*FinderSearchGetHotWordList)(nil), // 3: mm.FinderSearchGetHotWordList
	(*FinderSearchIdList)(nil),         // 4: mm.FinderSearchIdList
	(*BaseRequest)(nil),                // 5: BaseRequest
	(*FinderBaseRequest)(nil),          // 6: FinderBaseRequest
}
var file_finderSearchList_proto_depIdxs = []int32{
	5, // 0: mm.FinderSearchGetHotWordListRequest.baseRequest:type_name -> BaseRequest
	6, // 1: mm.FinderSearchGetHotWordListRequest.finderBaseRequest:type_name -> FinderBaseRequest
	2, // 2: mm.FinderSearchGetHotWordListResponse.baseResponse:type_name -> mm.Resp
	3, // 3: mm.FinderSearchGetHotWordListResponse.finderSearchGetHotWordList:type_name -> mm.FinderSearchGetHotWordList
	4, // 4: mm.FinderSearchGetHotWordList.data:type_name -> mm.FinderSearchIdList
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_finderSearchList_proto_init() }
func file_finderSearchList_proto_init() {
	if File_finderSearchList_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_finderSearchList_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinderSearchGetHotWordListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_finderSearchList_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_finderSearchList_proto_goTypes,
		DependencyIndexes: file_finderSearchList_proto_depIdxs,
		MessageInfos:      file_finderSearchList_proto_msgTypes,
	}.Build()
	File_finderSearchList_proto = out.File
	file_finderSearchList_proto_rawDesc = nil
	file_finderSearchList_proto_goTypes = nil
	file_finderSearchList_proto_depIdxs = nil
}
