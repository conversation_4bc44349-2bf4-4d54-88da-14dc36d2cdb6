syntax="proto2";

import "mm.proto";
package mm;
option go_package = "../mm";

message FinderSearchGetHotWordListRequest{
  optional BaseRequest baseRequest = 1;
  optional FinderBaseRequest finderBaseRequest=2;
}

message FinderSearchGetHotWordListResponse{
  optional  Resp baseResponse = 1;
  repeated FinderSearchGetHotWordList finderSearchGetHotWordList=2;
}
message Resp{
  optional int32 ret=1;
}
message FinderSearchGetHotWordList{
  optional string  title=1;
  optional int32   number=2;
  optional FinderSearchIdList  data=5;
}

message FinderSearchIdList{
  optional uint64 id=1;
  optional uint32 number=2;
}