// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        (unknown)
// source: BypSendResponse.proto

package mm

import (
	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type BypSendResp struct {
	Response []byte `protobuf:"bytes,1,opt,name=response" json:"response,omitempty"`
	MsgId    []byte `protobuf:"bytes,2,opt,name=msgId" json:"msgId,omitempty"`
}

func (x *BypSendResp) Reset() {
	*x = BypSendResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BypSendResponse_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BypSendResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BypSendResp) ProtoMessage() {}

func (x *BypSendResp) ProtoReflect() protoreflect.Message {
	mi := &file_BypSendResponse_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BypSendResp.ProtoReflect.Descriptor instead.
func (*BypSendResp) Descriptor() ([]byte, []int) {
	return file_BypSendResponse_proto_rawDescGZIP(), []int{0}
}

func (x *BypSendResp) GetResponse() []byte {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *BypSendResp) GetMsgId() []byte {
	if x != nil {
		return x.MsgId
	}
	return nil
}

var File_BypSendResponse_proto protoreflect.FileDescriptor

var file_BypSendResponse_proto_rawDesc = []byte{
	0x0a, 0x15, 0x42, 0x79, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x6d, 0x6d, 0x22, 0x2e, 0x0a, 0x0b, 0x42,
	0x79, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x08, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x12, 0x0d, 0x0a, 0x05,
	0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
}

var (
	file_BypSendResponse_proto_rawDescOnce sync.Once
	file_BypSendResponse_proto_rawDescData = file_BypSendResponse_proto_rawDesc
)

func file_BypSendResponse_proto_rawDescGZIP() []byte {
	file_BypSendResponse_proto_rawDescOnce.Do(func() {
		file_BypSendResponse_proto_rawDescData = protoimpl.X.CompressGZIP(file_BypSendResponse_proto_rawDescData)
	})
	return file_BypSendResponse_proto_rawDescData
}

var file_BypSendResponse_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_BypSendResponse_proto_goTypes = []interface{}{
	(*BypSendResp)(nil), // 0: mm.BypSendResp
}
var file_BypSendResponse_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_BypSendResponse_proto_init() }
func file_BypSendResponse_proto_init() {
	if File_BypSendResponse_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_BypSendResponse_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_BypSendResponse_proto_goTypes,
		DependencyIndexes: file_BypSendResponse_proto_depIdxs,
		MessageInfos:      file_BypSendResponse_proto_msgTypes,
	}.Build()
	File_BypSendResponse_proto = out.File
	file_BypSendResponse_proto_rawDesc = nil
	file_BypSendResponse_proto_goTypes = nil
	file_BypSendResponse_proto_depIdxs = nil
}
