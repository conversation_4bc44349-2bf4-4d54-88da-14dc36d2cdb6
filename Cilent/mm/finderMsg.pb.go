// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        (unknown)
// source: finderMsg.proto

package mm

import (
	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type FinderGetMsgSessionIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MyAccountType     *uint64            `protobuf:"varint,2,opt,name=myAccountType" json:"myAccountType,omitempty"`
	FinderUsername    *string            `protobuf:"bytes,3,opt,name=finderUsername" json:"finderUsername,omitempty"`
	FinderBaseRequest *FinderBaseRequest `protobuf:"bytes,4,opt,name=finderBaseRequest" json:"finderBaseRequest,omitempty"`
}

func (x *FinderGetMsgSessionIdRequest) Reset() {
	*x = FinderGetMsgSessionIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderGetMsgSessionIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderGetMsgSessionIdRequest) ProtoMessage() {}

func (x *FinderGetMsgSessionIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderGetMsgSessionIdRequest.ProtoReflect.Descriptor instead.
func (*FinderGetMsgSessionIdRequest) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{0}
}

func (x *FinderGetMsgSessionIdRequest) GetMyAccountType() uint64 {
	if x != nil && x.MyAccountType != nil {
		return *x.MyAccountType
	}
	return 0
}

func (x *FinderGetMsgSessionIdRequest) GetFinderUsername() string {
	if x != nil && x.FinderUsername != nil {
		return *x.FinderUsername
	}
	return ""
}

func (x *FinderGetMsgSessionIdRequest) GetFinderBaseRequest() *FinderBaseRequest {
	if x != nil {
		return x.FinderBaseRequest
	}
	return nil
}

type RespV struct {
	Ret *int32 `protobuf:"varint,1,opt,name=ret" json:"ret,omitempty"`
}

func (x *RespV) Reset() {
	*x = RespV{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RespV) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespV) ProtoMessage() {}

func (x *RespV) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespV.ProtoReflect.Descriptor instead.
func (*RespV) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{1}
}

func (x *RespV) GetRet() int32 {
	if x != nil && x.Ret != nil {
		return *x.Ret
	}
	return 0
}

type Response struct {
	Ret *int32             `protobuf:"varint,1,opt,name=ret" json:"ret,omitempty"`
	Sk  *SKBuiltinStringTs `protobuf:"bytes,2,opt,name=sk" json:"sk,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{2}
}

func (x *Response) GetRet() int32 {
	if x != nil && x.Ret != nil {
		return *x.Ret
	}
	return 0
}

func (x *Response) GetSk() *SKBuiltinStringTs {
	if x != nil {
		return x.Sk
	}
	return nil
}

type FinderGetMsgSessionIdResponse struct {
	Resp                 *RespV                `protobuf:"bytes,1,opt,name=resp" json:"resp,omitempty"`
	FinderMsg            *string               `protobuf:"bytes,2,opt,name=finderMsg" json:"finderMsg,omitempty"`
	EnableAction         *uint64               `protobuf:"varint,3,opt,name=enableAction" json:"enableAction,omitempty"`
	FinderUsername       *string               `protobuf:"bytes,4,opt,name=finderUsername" json:"finderUsername,omitempty"`
	FinderMsgSessionInfo *FinderMsgSessionInfo `protobuf:"bytes,5,opt,name=finderMsgSessionInfo" json:"finderMsgSessionInfo,omitempty"`
}

func (x *FinderGetMsgSessionIdResponse) Reset() {
	*x = FinderGetMsgSessionIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderGetMsgSessionIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderGetMsgSessionIdResponse) ProtoMessage() {}

func (x *FinderGetMsgSessionIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderGetMsgSessionIdResponse.ProtoReflect.Descriptor instead.
func (*FinderGetMsgSessionIdResponse) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{3}
}

func (x *FinderGetMsgSessionIdResponse) GetResp() *RespV {
	if x != nil {
		return x.Resp
	}
	return nil
}

func (x *FinderGetMsgSessionIdResponse) GetFinderMsg() string {
	if x != nil && x.FinderMsg != nil {
		return *x.FinderMsg
	}
	return ""
}

func (x *FinderGetMsgSessionIdResponse) GetEnableAction() uint64 {
	if x != nil && x.EnableAction != nil {
		return *x.EnableAction
	}
	return 0
}

func (x *FinderGetMsgSessionIdResponse) GetFinderUsername() string {
	if x != nil && x.FinderUsername != nil {
		return *x.FinderUsername
	}
	return ""
}

func (x *FinderGetMsgSessionIdResponse) GetFinderMsgSessionInfo() *FinderMsgSessionInfo {
	if x != nil {
		return x.FinderMsgSessionInfo
	}
	return nil
}

type FinderMsgSessionInfo struct {
	FinderUsername *string `protobuf:"bytes,1,opt,name=finderUsername" json:"finderUsername,omitempty"`
	SessionId      *string `protobuf:"bytes,2,opt,name=sessionId" json:"sessionId,omitempty"`
	RejectMsg      *uint64 `protobuf:"varint,3,opt,name=rejectMsg" json:"rejectMsg,omitempty"`
	EnableAction   *uint64 `protobuf:"varint,4,opt,name=enableAction" json:"enableAction,omitempty"`
}

func (x *FinderMsgSessionInfo) Reset() {
	*x = FinderMsgSessionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderMsgSessionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderMsgSessionInfo) ProtoMessage() {}

func (x *FinderMsgSessionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderMsgSessionInfo.ProtoReflect.Descriptor instead.
func (*FinderMsgSessionInfo) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{4}
}

func (x *FinderMsgSessionInfo) GetFinderUsername() string {
	if x != nil && x.FinderUsername != nil {
		return *x.FinderUsername
	}
	return ""
}

func (x *FinderMsgSessionInfo) GetSessionId() string {
	if x != nil && x.SessionId != nil {
		return *x.SessionId
	}
	return ""
}

func (x *FinderMsgSessionInfo) GetRejectMsg() uint64 {
	if x != nil && x.RejectMsg != nil {
		return *x.RejectMsg
	}
	return 0
}

func (x *FinderMsgSessionInfo) GetEnableAction() uint64 {
	if x != nil && x.EnableAction != nil {
		return *x.EnableAction
	}
	return 0
}

type FinderGetContactListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FinderBaseRequest *FinderBaseRequest `protobuf:"bytes,2,opt,name=finderBaseRequest" json:"finderBaseRequest,omitempty"`
	FinderUsername    *string            `protobuf:"bytes,3,opt,name=finderUsername" json:"finderUsername,omitempty"`
	ContactType       *uint64            `protobuf:"varint,4,opt,name=contactType" json:"contactType,omitempty"`
}

func (x *FinderGetContactListReq) Reset() {
	*x = FinderGetContactListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderGetContactListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderGetContactListReq) ProtoMessage() {}

func (x *FinderGetContactListReq) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderGetContactListReq.ProtoReflect.Descriptor instead.
func (*FinderGetContactListReq) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{5}
}

func (x *FinderGetContactListReq) GetFinderBaseRequest() *FinderBaseRequest {
	if x != nil {
		return x.FinderBaseRequest
	}
	return nil
}

func (x *FinderGetContactListReq) GetFinderUsername() string {
	if x != nil && x.FinderUsername != nil {
		return *x.FinderUsername
	}
	return ""
}

func (x *FinderGetContactListReq) GetContactType() uint64 {
	if x != nil && x.ContactType != nil {
		return *x.ContactType
	}
	return 0
}

type FinderGetContactListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resp *RespV `protobuf:"bytes,1,opt,name=resp" json:"resp,omitempty"`
}

func (x *FinderGetContactListResp) Reset() {
	*x = FinderGetContactListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderGetContactListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderGetContactListResp) ProtoMessage() {}

func (x *FinderGetContactListResp) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderGetContactListResp.ProtoReflect.Descriptor instead.
func (*FinderGetContactListResp) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{6}
}

func (x *FinderGetContactListResp) GetResp() *RespV {
	if x != nil {
		return x.Resp
	}
	return nil
}

type ContactList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FinderUsername *string `protobuf:"bytes,1,opt,name=finderUsername" json:"finderUsername,omitempty"`
	NickName       *string `protobuf:"bytes,2,opt,name=nickName" json:"nickName,omitempty"`
	Desc           *string `protobuf:"bytes,3,opt,name=desc" json:"desc,omitempty"`
	ListV6         *uint64 `protobuf:"varint,6,opt,name=listV6" json:"listV6,omitempty"`
	ImgBuf         *string `protobuf:"bytes,9,opt,name=imgBuf" json:"imgBuf,omitempty"`
	BitMask        *uint64 `protobuf:"varint,10,opt,name=bitMask" json:"bitMask,omitempty"`
	BitVal         *uint64 `protobuf:"varint,11,opt,name=bitVal" json:"bitVal,omitempty"`
	Region         *Region `protobuf:"bytes,12,opt,name=region" json:"region,omitempty"`
	V15            *uint64 `protobuf:"varint,15,opt,name=v15" json:"v15,omitempty"`
	V17            *uint64 `protobuf:"varint,17,opt,name=v17" json:"v17,omitempty"`
	V18            *string `protobuf:"bytes,18,opt,name=v18" json:"v18,omitempty"`
}

func (x *ContactList) Reset() {
	*x = ContactList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContactList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactList) ProtoMessage() {}

func (x *ContactList) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactList.ProtoReflect.Descriptor instead.
func (*ContactList) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{7}
}

func (x *ContactList) GetFinderUsername() string {
	if x != nil && x.FinderUsername != nil {
		return *x.FinderUsername
	}
	return ""
}

func (x *ContactList) GetNickName() string {
	if x != nil && x.NickName != nil {
		return *x.NickName
	}
	return ""
}

func (x *ContactList) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *ContactList) GetListV6() uint64 {
	if x != nil && x.ListV6 != nil {
		return *x.ListV6
	}
	return 0
}

func (x *ContactList) GetImgBuf() string {
	if x != nil && x.ImgBuf != nil {
		return *x.ImgBuf
	}
	return ""
}

func (x *ContactList) GetBitMask() uint64 {
	if x != nil && x.BitMask != nil {
		return *x.BitMask
	}
	return 0
}

func (x *ContactList) GetBitVal() uint64 {
	if x != nil && x.BitVal != nil {
		return *x.BitVal
	}
	return 0
}

func (x *ContactList) GetRegion() *Region {
	if x != nil {
		return x.Region
	}
	return nil
}

func (x *ContactList) GetV15() uint64 {
	if x != nil && x.V15 != nil {
		return *x.V15
	}
	return 0
}

func (x *ContactList) GetV17() uint64 {
	if x != nil && x.V17 != nil {
		return *x.V17
	}
	return 0
}

func (x *ContactList) GetV18() string {
	if x != nil && x.V18 != nil {
		return *x.V18
	}
	return ""
}

type Region struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region   *string `protobuf:"bytes,1,opt,name=region" json:"region,omitempty"`
	Province *string `protobuf:"bytes,2,opt,name=province" json:"province,omitempty"`
	City     *string `protobuf:"bytes,3,opt,name=city" json:"city,omitempty"`
	Reg4     *uint64 `protobuf:"varint,4,opt,name=reg4" json:"reg4,omitempty"`
}

func (x *Region) Reset() {
	*x = Region{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Region) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Region) ProtoMessage() {}

func (x *Region) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Region.ProtoReflect.Descriptor instead.
func (*Region) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{8}
}

func (x *Region) GetRegion() string {
	if x != nil && x.Region != nil {
		return *x.Region
	}
	return ""
}

func (x *Region) GetProvince() string {
	if x != nil && x.Province != nil {
		return *x.Province
	}
	return ""
}

func (x *Region) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *Region) GetReg4() uint64 {
	if x != nil && x.Reg4 != nil {
		return *x.Reg4
	}
	return 0
}

type BypSendRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRequest *BaseRequest `protobuf:"bytes,1,opt,name=baseRequest" json:"baseRequest,omitempty"`
	BizType     *uint64      `protobuf:"varint,2,opt,name=bizType" json:"bizType,omitempty"`
	BypMsgPack  *BypMsgPack  `protobuf:"bytes,3,opt,name=bypMsgPack" json:"bypMsgPack,omitempty"`
}

func (x *BypSendRequest) Reset() {
	*x = BypSendRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BypSendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BypSendRequest) ProtoMessage() {}

func (x *BypSendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BypSendRequest.ProtoReflect.Descriptor instead.
func (*BypSendRequest) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{9}
}

func (x *BypSendRequest) GetBaseRequest() *BaseRequest {
	if x != nil {
		return x.BaseRequest
	}
	return nil
}

func (x *BypSendRequest) GetBizType() uint64 {
	if x != nil && x.BizType != nil {
		return *x.BizType
	}
	return 0
}

func (x *BypSendRequest) GetBypMsgPack() *BypMsgPack {
	if x != nil {
		return x.BypMsgPack
	}
	return nil
}

type BypSendResponse struct {
	Response *Response `protobuf:"bytes,1,opt,name=response" json:"response,omitempty"`
	MsgId    *uint64   `protobuf:"varint,2,opt,name=msgId" json:"msgId,omitempty"`
}

func (x *BypSendResponse) Reset() {
	*x = BypSendResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BypSendResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BypSendResponse) ProtoMessage() {}

func (x *BypSendResponse) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BypSendResponse.ProtoReflect.Descriptor instead.
func (*BypSendResponse) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{10}
}

func (x *BypSendResponse) GetResponse() *Response {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *BypSendResponse) GetMsgId() uint64 {
	if x != nil && x.MsgId != nil {
		return *x.MsgId
	}
	return 0
}

type MsgExtInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info   []*EInfo `protobuf:"bytes,1,rep,name=info" json:"info,omitempty"`
	InfoV2 *uint64  `protobuf:"varint,2,opt,name=infoV2" json:"infoV2,omitempty"`
}

func (x *MsgExtInfo) Reset() {
	*x = MsgExtInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgExtInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgExtInfo) ProtoMessage() {}

func (x *MsgExtInfo) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgExtInfo.ProtoReflect.Descriptor instead.
func (*MsgExtInfo) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{11}
}

func (x *MsgExtInfo) GetInfo() []*EInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *MsgExtInfo) GetInfoV2() uint64 {
	if x != nil && x.InfoV2 != nil {
		return *x.InfoV2
	}
	return 0
}

type EInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EinfoV1 *uint64 `protobuf:"varint,1,opt,name=einfoV1" json:"einfoV1,omitempty"`
	EinfoV2 *uint64 `protobuf:"varint,2,opt,name=einfoV2" json:"einfoV2,omitempty"`
}

func (x *EInfo) Reset() {
	*x = EInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EInfo) ProtoMessage() {}

func (x *EInfo) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EInfo.ProtoReflect.Descriptor instead.
func (*EInfo) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{12}
}

func (x *EInfo) GetEinfoV1() uint64 {
	if x != nil && x.EinfoV1 != nil {
		return *x.EinfoV1
	}
	return 0
}

func (x *EInfo) GetEinfoV2() uint64 {
	if x != nil && x.EinfoV2 != nil {
		return *x.EinfoV2
	}
	return 0
}

type BypMsgPack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgType      *uint64            `protobuf:"varint,1,opt,name=msgType" json:"msgType,omitempty"`
	FromUsername *string            `protobuf:"bytes,2,opt,name=fromUsername" json:"fromUsername,omitempty"`
	ToUsername   *string            `protobuf:"bytes,3,opt,name=toUsername" json:"toUsername,omitempty"`
	MsgSessionId *string            `protobuf:"bytes,4,opt,name=msgSessionId" json:"msgSessionId,omitempty"`
	MsgSource    *string            `protobuf:"bytes,5,opt,name=msgSource" json:"msgSource,omitempty"`
	V6           *string            `protobuf:"bytes,6,opt,name=v6" json:"v6,omitempty"`
	SessionId    *string            `protobuf:"bytes,7,opt,name=sessionId" json:"sessionId,omitempty"`
	Sk           *SKBuiltinStringTs `protobuf:"bytes,101,opt,name=sk" json:"sk,omitempty"`
	SkImg        *BypMsgImgPack     `protobuf:"bytes,102,opt,name=skImg" json:"skImg,omitempty"`
	SkVideo      *BypMsgVideoPack   `protobuf:"bytes,103,opt,name=skVideo" json:"skVideo,omitempty"`
}

func (x *BypMsgPack) Reset() {
	*x = BypMsgPack{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BypMsgPack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BypMsgPack) ProtoMessage() {}

func (x *BypMsgPack) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BypMsgPack.ProtoReflect.Descriptor instead.
func (*BypMsgPack) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{13}
}

func (x *BypMsgPack) GetMsgType() uint64 {
	if x != nil && x.MsgType != nil {
		return *x.MsgType
	}
	return 0
}

func (x *BypMsgPack) GetFromUsername() string {
	if x != nil && x.FromUsername != nil {
		return *x.FromUsername
	}
	return ""
}

func (x *BypMsgPack) GetToUsername() string {
	if x != nil && x.ToUsername != nil {
		return *x.ToUsername
	}
	return ""
}

func (x *BypMsgPack) GetMsgSessionId() string {
	if x != nil && x.MsgSessionId != nil {
		return *x.MsgSessionId
	}
	return ""
}

func (x *BypMsgPack) GetMsgSource() string {
	if x != nil && x.MsgSource != nil {
		return *x.MsgSource
	}
	return ""
}

func (x *BypMsgPack) GetV6() string {
	if x != nil && x.V6 != nil {
		return *x.V6
	}
	return ""
}

func (x *BypMsgPack) GetSessionId() string {
	if x != nil && x.SessionId != nil {
		return *x.SessionId
	}
	return ""
}

func (x *BypMsgPack) GetSk() *SKBuiltinStringTs {
	if x != nil {
		return x.Sk
	}
	return nil
}

func (x *BypMsgPack) GetSkImg() *BypMsgImgPack {
	if x != nil {
		return x.SkImg
	}
	return nil
}

func (x *BypMsgPack) GetSkVideo() *BypMsgVideoPack {
	if x != nil {
		return x.SkVideo
	}
	return nil
}

type BypMsgVideoPack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BypVideoV1  *string `protobuf:"bytes,1,opt,name=bypVideoV1" json:"bypVideoV1,omitempty"`
	BypVideoV2  *string `protobuf:"bytes,2,opt,name=bypVideoV2" json:"bypVideoV2,omitempty"`
	BypVideoV3  *uint64 `protobuf:"varint,3,opt,name=bypVideoV3" json:"bypVideoV3,omitempty"`
	BypVideoV4  *uint64 `protobuf:"varint,4,opt,name=bypVideoV4" json:"bypVideoV4,omitempty"`
	BypVideoV5  *uint64 `protobuf:"varint,5,opt,name=bypVideoV5" json:"bypVideoV5,omitempty"`
	BypVideoV6  *string `protobuf:"bytes,6,opt,name=bypVideoV6" json:"bypVideoV6,omitempty"`
	BypVideoV7  *string `protobuf:"bytes,7,opt,name=bypVideoV7" json:"bypVideoV7,omitempty"`
	BypVideoV8  *uint64 `protobuf:"varint,8,opt,name=bypVideoV8" json:"bypVideoV8,omitempty"`
	BypVideoV9  *uint64 `protobuf:"varint,9,opt,name=bypVideoV9" json:"bypVideoV9,omitempty"`
	BypVideoV10 *uint64 `protobuf:"varint,10,opt,name=bypVideoV10" json:"bypVideoV10,omitempty"`
	BypVideoV11 *string `protobuf:"bytes,11,opt,name=bypVideoV11" json:"bypVideoV11,omitempty"`
	BypVideoV12 *string `protobuf:"bytes,12,opt,name=bypVideoV12" json:"bypVideoV12,omitempty"`
	BypVideoV13 *string `protobuf:"bytes,13,opt,name=bypVideoV13" json:"bypVideoV13,omitempty"`
}

func (x *BypMsgVideoPack) Reset() {
	*x = BypMsgVideoPack{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BypMsgVideoPack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BypMsgVideoPack) ProtoMessage() {}

func (x *BypMsgVideoPack) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BypMsgVideoPack.ProtoReflect.Descriptor instead.
func (*BypMsgVideoPack) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{14}
}

func (x *BypMsgVideoPack) GetBypVideoV1() string {
	if x != nil && x.BypVideoV1 != nil {
		return *x.BypVideoV1
	}
	return ""
}

func (x *BypMsgVideoPack) GetBypVideoV2() string {
	if x != nil && x.BypVideoV2 != nil {
		return *x.BypVideoV2
	}
	return ""
}

func (x *BypMsgVideoPack) GetBypVideoV3() uint64 {
	if x != nil && x.BypVideoV3 != nil {
		return *x.BypVideoV3
	}
	return 0
}

func (x *BypMsgVideoPack) GetBypVideoV4() uint64 {
	if x != nil && x.BypVideoV4 != nil {
		return *x.BypVideoV4
	}
	return 0
}

func (x *BypMsgVideoPack) GetBypVideoV5() uint64 {
	if x != nil && x.BypVideoV5 != nil {
		return *x.BypVideoV5
	}
	return 0
}

func (x *BypMsgVideoPack) GetBypVideoV6() string {
	if x != nil && x.BypVideoV6 != nil {
		return *x.BypVideoV6
	}
	return ""
}

func (x *BypMsgVideoPack) GetBypVideoV7() string {
	if x != nil && x.BypVideoV7 != nil {
		return *x.BypVideoV7
	}
	return ""
}

func (x *BypMsgVideoPack) GetBypVideoV8() uint64 {
	if x != nil && x.BypVideoV8 != nil {
		return *x.BypVideoV8
	}
	return 0
}

func (x *BypMsgVideoPack) GetBypVideoV9() uint64 {
	if x != nil && x.BypVideoV9 != nil {
		return *x.BypVideoV9
	}
	return 0
}

func (x *BypMsgVideoPack) GetBypVideoV10() uint64 {
	if x != nil && x.BypVideoV10 != nil {
		return *x.BypVideoV10
	}
	return 0
}

func (x *BypMsgVideoPack) GetBypVideoV11() string {
	if x != nil && x.BypVideoV11 != nil {
		return *x.BypVideoV11
	}
	return ""
}

func (x *BypMsgVideoPack) GetBypVideoV12() string {
	if x != nil && x.BypVideoV12 != nil {
		return *x.BypVideoV12
	}
	return ""
}

func (x *BypMsgVideoPack) GetBypVideoV13() string {
	if x != nil && x.BypVideoV13 != nil {
		return *x.BypVideoV13
	}
	return ""
}

type BypMsgImgPack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BypV1        *string `protobuf:"bytes,1,opt,name=bypV1" json:"bypV1,omitempty"`
	FromUsername *string `protobuf:"bytes,2,opt,name=fromUsername" json:"fromUsername,omitempty"`
	BypV3        *string `protobuf:"bytes,3,opt,name=bypV3" json:"bypV3,omitempty"`
	BypV4        *uint64 `protobuf:"varint,4,opt,name=bypV4" json:"bypV4,omitempty"`
	BypV5        *uint64 `protobuf:"varint,5,opt,name=bypV5" json:"bypV5,omitempty"`
	BypV6        *uint64 `protobuf:"varint,6,opt,name=bypV6" json:"bypV6,omitempty"`
	BypV7        *uint64 `protobuf:"varint,7,opt,name=bypV7" json:"bypV7,omitempty"`
	BypV8        *uint64 `protobuf:"varint,8,opt,name=bypV8" json:"bypV8,omitempty"`
	BypV9        *string `protobuf:"bytes,9,opt,name=bypV9" json:"bypV9,omitempty"`
	BypV15       *uint64 `protobuf:"varint,15,opt,name=bypV15" json:"bypV15,omitempty"`
}

func (x *BypMsgImgPack) Reset() {
	*x = BypMsgImgPack{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BypMsgImgPack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BypMsgImgPack) ProtoMessage() {}

func (x *BypMsgImgPack) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BypMsgImgPack.ProtoReflect.Descriptor instead.
func (*BypMsgImgPack) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{15}
}

func (x *BypMsgImgPack) GetBypV1() string {
	if x != nil && x.BypV1 != nil {
		return *x.BypV1
	}
	return ""
}

func (x *BypMsgImgPack) GetFromUsername() string {
	if x != nil && x.FromUsername != nil {
		return *x.FromUsername
	}
	return ""
}

func (x *BypMsgImgPack) GetBypV3() string {
	if x != nil && x.BypV3 != nil {
		return *x.BypV3
	}
	return ""
}

func (x *BypMsgImgPack) GetBypV4() uint64 {
	if x != nil && x.BypV4 != nil {
		return *x.BypV4
	}
	return 0
}

func (x *BypMsgImgPack) GetBypV5() uint64 {
	if x != nil && x.BypV5 != nil {
		return *x.BypV5
	}
	return 0
}

func (x *BypMsgImgPack) GetBypV6() uint64 {
	if x != nil && x.BypV6 != nil {
		return *x.BypV6
	}
	return 0
}

func (x *BypMsgImgPack) GetBypV7() uint64 {
	if x != nil && x.BypV7 != nil {
		return *x.BypV7
	}
	return 0
}

func (x *BypMsgImgPack) GetBypV8() uint64 {
	if x != nil && x.BypV8 != nil {
		return *x.BypV8
	}
	return 0
}

func (x *BypMsgImgPack) GetBypV9() string {
	if x != nil && x.BypV9 != nil {
		return *x.BypV9
	}
	return ""
}

func (x *BypMsgImgPack) GetBypV15() uint64 {
	if x != nil && x.BypV15 != nil {
		return *x.BypV15
	}
	return 0
}

type BypSyncRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRequest *BaseRequest `protobuf:"bytes,1,opt,name=baseRequest" json:"baseRequest,omitempty"`
	Scene       *uint64      `protobuf:"varint,2,opt,name=scene" json:"scene,omitempty"`
	MsgExtInfo  *MsgExtInfo  `protobuf:"bytes,3,opt,name=msgExtInfo" json:"msgExtInfo,omitempty"`
	UExt        *uint64      `protobuf:"varint,4,opt,name=uExt" json:"uExt,omitempty"`
}

func (x *BypSyncRequest) Reset() {
	*x = BypSyncRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BypSyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BypSyncRequest) ProtoMessage() {}

func (x *BypSyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BypSyncRequest.ProtoReflect.Descriptor instead.
func (*BypSyncRequest) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{16}
}

func (x *BypSyncRequest) GetBaseRequest() *BaseRequest {
	if x != nil {
		return x.BaseRequest
	}
	return nil
}

func (x *BypSyncRequest) GetScene() uint64 {
	if x != nil && x.Scene != nil {
		return *x.Scene
	}
	return 0
}

func (x *BypSyncRequest) GetMsgExtInfo() *MsgExtInfo {
	if x != nil {
		return x.MsgExtInfo
	}
	return nil
}

func (x *BypSyncRequest) GetUExt() uint64 {
	if x != nil && x.UExt != nil {
		return *x.UExt
	}
	return 0
}

type BypSyncResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Response     *Response     `protobuf:"bytes,1,opt,name=response" json:"response,omitempty"`
	MsgExtInfo   *MsgExtInfo   `protobuf:"bytes,3,opt,name=msgExtInfo" json:"msgExtInfo,omitempty"`
	SyncItemList *SyncItemList `protobuf:"bytes,4,opt,name=syncItemList" json:"syncItemList,omitempty"`
}

func (x *BypSyncResponse) Reset() {
	*x = BypSyncResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BypSyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BypSyncResponse) ProtoMessage() {}

func (x *BypSyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BypSyncResponse.ProtoReflect.Descriptor instead.
func (*BypSyncResponse) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{17}
}

func (x *BypSyncResponse) GetResponse() *Response {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *BypSyncResponse) GetMsgExtInfo() *MsgExtInfo {
	if x != nil {
		return x.MsgExtInfo
	}
	return nil
}

func (x *BypSyncResponse) GetSyncItemList() *SyncItemList {
	if x != nil {
		return x.SyncItemList
	}
	return nil
}

type SyncItemList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SyncItemV1 *uint64   `protobuf:"varint,1,opt,name=syncItemV1" json:"syncItemV1,omitempty"`
	SyncItemV2 *uint64   `protobuf:"varint,2,opt,name=syncItemV2" json:"syncItemV2,omitempty"`
	SyncItem   *SyncItem `protobuf:"bytes,3,opt,name=syncItem" json:"syncItem,omitempty"`
}

func (x *SyncItemList) Reset() {
	*x = SyncItemList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncItemList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncItemList) ProtoMessage() {}

func (x *SyncItemList) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncItemList.ProtoReflect.Descriptor instead.
func (*SyncItemList) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{18}
}

func (x *SyncItemList) GetSyncItemV1() uint64 {
	if x != nil && x.SyncItemV1 != nil {
		return *x.SyncItemV1
	}
	return 0
}

func (x *SyncItemList) GetSyncItemV2() uint64 {
	if x != nil && x.SyncItemV2 != nil {
		return *x.SyncItemV2
	}
	return 0
}

func (x *SyncItemList) GetSyncItem() *SyncItem {
	if x != nil {
		return x.SyncItem
	}
	return nil
}

type SyncItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FinderSync   *FinderSync `protobuf:"bytes,1,opt,name=finderSync" json:"finderSync,omitempty"`
	FromUsername *string     `protobuf:"bytes,2,opt,name=fromUsername" json:"fromUsername,omitempty"`
	SyncItemV3   *string     `protobuf:"bytes,3,opt,name=SyncItemV3" json:"SyncItemV3,omitempty"`
	SyncInfo     *EInfo      `protobuf:"bytes,4,opt,name=syncInfo" json:"syncInfo,omitempty"`
	SyncV5       *uint64     `protobuf:"varint,5,opt,name=syncV5" json:"syncV5,omitempty"`
}

func (x *SyncItem) Reset() {
	*x = SyncItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncItem) ProtoMessage() {}

func (x *SyncItem) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncItem.ProtoReflect.Descriptor instead.
func (*SyncItem) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{19}
}

func (x *SyncItem) GetFinderSync() *FinderSync {
	if x != nil {
		return x.FinderSync
	}
	return nil
}

func (x *SyncItem) GetFromUsername() string {
	if x != nil && x.FromUsername != nil {
		return *x.FromUsername
	}
	return ""
}

func (x *SyncItem) GetSyncItemV3() string {
	if x != nil && x.SyncItemV3 != nil {
		return *x.SyncItemV3
	}
	return ""
}

func (x *SyncItem) GetSyncInfo() *EInfo {
	if x != nil {
		return x.SyncInfo
	}
	return nil
}

func (x *SyncItem) GetSyncV5() uint64 {
	if x != nil && x.SyncV5 != nil {
		return *x.SyncV5
	}
	return 0
}

type FinderSync struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FinderSyncV1  *uint64            `protobuf:"varint,1,opt,name=finderSyncV1" json:"finderSyncV1,omitempty"`
	FinderSyncV2  *SKBuiltinStringTs `protobuf:"bytes,2,opt,name=finderSyncV2" json:"finderSyncV2,omitempty"`
	FinderSyncV3  *SKBuiltinStringTs `protobuf:"bytes,3,opt,name=finderSyncV3" json:"finderSyncV3,omitempty"`
	FinderSyncV4  *uint64            `protobuf:"varint,4,opt,name=finderSyncV4" json:"finderSyncV4,omitempty"`
	FinderSyncV5  *SKBuiltinStringTs `protobuf:"bytes,5,opt,name=finderSyncV5" json:"finderSyncV5,omitempty"`
	FinderSyncV6  *uint64            `protobuf:"varint,6,opt,name=finderSyncV6" json:"finderSyncV6,omitempty"`
	FinderSyncV7  *uint64            `protobuf:"varint,7,opt,name=finderSyncV7" json:"finderSyncV7,omitempty"`
	FinderSyncV9  *uint64            `protobuf:"varint,9,opt,name=finderSyncV9" json:"finderSyncV9,omitempty"`
	FinderSyncV10 *string            `protobuf:"bytes,10,opt,name=finderSyncV10" json:"finderSyncV10,omitempty"`
	FinderSyncV12 *uint64            `protobuf:"varint,12,opt,name=finderSyncV12" json:"finderSyncV12,omitempty"`
	FinderSyncV13 *uint64            `protobuf:"varint,13,opt,name=finderSyncV13" json:"finderSyncV13,omitempty"`
}

func (x *FinderSync) Reset() {
	*x = FinderSync{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinderSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinderSync) ProtoMessage() {}

func (x *FinderSync) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinderSync.ProtoReflect.Descriptor instead.
func (*FinderSync) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{20}
}

func (x *FinderSync) GetFinderSyncV1() uint64 {
	if x != nil && x.FinderSyncV1 != nil {
		return *x.FinderSyncV1
	}
	return 0
}

func (x *FinderSync) GetFinderSyncV2() *SKBuiltinStringTs {
	if x != nil {
		return x.FinderSyncV2
	}
	return nil
}

func (x *FinderSync) GetFinderSyncV3() *SKBuiltinStringTs {
	if x != nil {
		return x.FinderSyncV3
	}
	return nil
}

func (x *FinderSync) GetFinderSyncV4() uint64 {
	if x != nil && x.FinderSyncV4 != nil {
		return *x.FinderSyncV4
	}
	return 0
}

func (x *FinderSync) GetFinderSyncV5() *SKBuiltinStringTs {
	if x != nil {
		return x.FinderSyncV5
	}
	return nil
}

func (x *FinderSync) GetFinderSyncV6() uint64 {
	if x != nil && x.FinderSyncV6 != nil {
		return *x.FinderSyncV6
	}
	return 0
}

func (x *FinderSync) GetFinderSyncV7() uint64 {
	if x != nil && x.FinderSyncV7 != nil {
		return *x.FinderSyncV7
	}
	return 0
}

func (x *FinderSync) GetFinderSyncV9() uint64 {
	if x != nil && x.FinderSyncV9 != nil {
		return *x.FinderSyncV9
	}
	return 0
}

func (x *FinderSync) GetFinderSyncV10() string {
	if x != nil && x.FinderSyncV10 != nil {
		return *x.FinderSyncV10
	}
	return ""
}

func (x *FinderSync) GetFinderSyncV12() uint64 {
	if x != nil && x.FinderSyncV12 != nil {
		return *x.FinderSyncV12
	}
	return 0
}

func (x *FinderSync) GetFinderSyncV13() uint64 {
	if x != nil && x.FinderSyncV13 != nil {
		return *x.FinderSyncV13
	}
	return 0
}

type SKBuiltinStringTs struct {
	String_ *string `protobuf:"bytes,1,opt,name=string" json:"string,omitempty"`
}

func (x *SKBuiltinStringTs) Reset() {
	*x = SKBuiltinStringTs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_finderMsg_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SKBuiltinStringTs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SKBuiltinStringTs) ProtoMessage() {}

func (x *SKBuiltinStringTs) ProtoReflect() protoreflect.Message {
	mi := &file_finderMsg_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SKBuiltinStringTs.ProtoReflect.Descriptor instead.
func (*SKBuiltinStringTs) Descriptor() ([]byte, []int) {
	return file_finderMsg_proto_rawDescGZIP(), []int{21}
}

func (x *SKBuiltinStringTs) GetString_() string {
	if x != nil && x.String_ != nil {
		return *x.String_
	}
	return ""
}

var File_finderMsg_proto protoreflect.FileDescriptor

var file_finderMsg_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x73, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x02, 0x6d, 0x6d, 0x1a, 0x08, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x7c, 0x0a, 0x1c, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x47, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x0d, 0x6d, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x12, 0x16, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x2d,
	0x0a, 0x11, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x46, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x14, 0x0a,
	0x05, 0x52, 0x65, 0x73, 0x70, 0x56, 0x12, 0x0b, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x22, 0x3b, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x0b, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x12, 0x22, 0x0a, 0x02,
	0x73, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x6d, 0x2e, 0x53, 0x4b,
	0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x73,
	0x22, 0xb1, 0x01, 0x0a, 0x1d, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x47, 0x65, 0x74, 0x4d, 0x73,
	0x67, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x72, 0x65, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x09, 0x2e, 0x6d, 0x6d, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x56, 0x12, 0x11, 0x0a, 0x09, 0x66,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x14,
	0x0a, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x12, 0x16, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x55, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x12, 0x36, 0x0a, 0x14,
	0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x73, 0x67, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x6d, 0x2e,
	0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x73, 0x67, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6a, 0x0a, 0x14, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x73,
	0x67, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x0e,
	0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x11, 0x0a, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x11, 0x0a, 0x09, 0x72, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x12, 0x14, 0x0a, 0x0c, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x22, 0x75, 0x0a, 0x17, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x11, 0x66,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x0e, 0x66, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x12, 0x13, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x22, 0x33, 0x0a, 0x18, 0x46, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x04, 0x72, 0x65, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x09, 0x2e, 0x6d, 0x6d, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x56, 0x22, 0xc9, 0x01, 0x0a,
	0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x0e,
	0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x0e, 0x0a, 0x06, 0x6c, 0x69, 0x73, 0x74, 0x56, 0x36, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x04, 0x12, 0x0e, 0x0a, 0x06, 0x69, 0x6d, 0x67, 0x42, 0x75, 0x66, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x0f, 0x0a, 0x07, 0x62, 0x69, 0x74, 0x4d, 0x61, 0x73, 0x6b, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0e, 0x0a, 0x06, 0x62, 0x69, 0x74, 0x56, 0x61, 0x6c, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x04, 0x12, 0x1a, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x6d, 0x6d, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x0b, 0x0a, 0x03, 0x76, 0x31, 0x35, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0b,
	0x0a, 0x03, 0x76, 0x31, 0x37, 0x18, 0x11, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0b, 0x0a, 0x03, 0x76,
	0x31, 0x38, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x22, 0x46, 0x0a, 0x06, 0x52, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04, 0x72, 0x65, 0x67, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x22, 0x68, 0x0a, 0x0e, 0x42, 0x79, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0f, 0x0a, 0x07, 0x62, 0x69, 0x7a, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x12, 0x22, 0x0a, 0x0a, 0x62, 0x79, 0x70, 0x4d, 0x73, 0x67,
	0x50, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6d, 0x6d, 0x2e,
	0x42, 0x79, 0x70, 0x4d, 0x73, 0x67, 0x50, 0x61, 0x63, 0x6b, 0x22, 0x40, 0x0a, 0x0f, 0x42, 0x79,
	0x70, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a,
	0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x6d, 0x6d, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0d, 0x0a,
	0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x22, 0x35, 0x0a, 0x0a,
	0x4d, 0x73, 0x67, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x6d, 0x6d, 0x2e, 0x45, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x06, 0x69, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x22, 0x29, 0x0a, 0x05, 0x45, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0f, 0x0a, 0x07,
	0x65, 0x69, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0f, 0x0a,
	0x07, 0x65, 0x69, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x22, 0xfb,
	0x01, 0x0a, 0x0a, 0x42, 0x79, 0x70, 0x4d, 0x73, 0x67, 0x50, 0x61, 0x63, 0x6b, 0x12, 0x0f, 0x0a,
	0x07, 0x6d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x12, 0x14,
	0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x12, 0x0a, 0x0a, 0x74, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x14, 0x0a, 0x0c, 0x6d, 0x73, 0x67, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x12, 0x11,
	0x0a, 0x09, 0x6d, 0x73, 0x67, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x0a, 0x0a, 0x02, 0x76, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x12, 0x11, 0x0a,
	0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x22, 0x0a, 0x02, 0x73, 0x6b, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d,
	0x6d, 0x2e, 0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x05, 0x73, 0x6b, 0x49, 0x6d, 0x67, 0x18, 0x66, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x6d, 0x2e, 0x42, 0x79, 0x70, 0x4d, 0x73, 0x67, 0x49,
	0x6d, 0x67, 0x50, 0x61, 0x63, 0x6b, 0x12, 0x24, 0x0a, 0x07, 0x73, 0x6b, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x18, 0x67, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6d, 0x6d, 0x2e, 0x42, 0x79, 0x70,
	0x4d, 0x73, 0x67, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x61, 0x63, 0x6b, 0x22, 0x99, 0x02, 0x0a,
	0x0f, 0x42, 0x79, 0x70, 0x4d, 0x73, 0x67, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x61, 0x63, 0x6b,
	0x12, 0x12, 0x0a, 0x0a, 0x62, 0x79, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x31, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x12, 0x0a, 0x0a, 0x62, 0x79, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x56, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x12, 0x0a, 0x0a, 0x62, 0x79, 0x70, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x56, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x12, 0x12, 0x0a, 0x0a,
	0x62, 0x79, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x12, 0x12, 0x0a, 0x0a, 0x62, 0x79, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x35, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x12, 0x12, 0x0a, 0x0a, 0x62, 0x79, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x56, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x12, 0x12, 0x0a, 0x0a, 0x62, 0x79, 0x70, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x56, 0x37, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x12, 0x12, 0x0a, 0x0a,
	0x62, 0x79, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x38, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04,
	0x12, 0x12, 0x0a, 0x0a, 0x62, 0x79, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x39, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x04, 0x12, 0x13, 0x0a, 0x0b, 0x62, 0x79, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x56, 0x31, 0x30, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x12, 0x13, 0x0a, 0x0b, 0x62, 0x79, 0x70,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x31, 0x31, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x12, 0x13,
	0x0a, 0x0b, 0x62, 0x79, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x31, 0x32, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x12, 0x13, 0x0a, 0x0b, 0x62, 0x79, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x56,
	0x31, 0x33, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x22, 0xad, 0x01, 0x0a, 0x0d, 0x42, 0x79, 0x70,
	0x4d, 0x73, 0x67, 0x49, 0x6d, 0x67, 0x50, 0x61, 0x63, 0x6b, 0x12, 0x0d, 0x0a, 0x05, 0x62, 0x79,
	0x70, 0x56, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x14, 0x0a, 0x0c, 0x66, 0x72, 0x6f,
	0x6d, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x0d, 0x0a, 0x05, 0x62, 0x79, 0x70, 0x56, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0d,
	0x0a, 0x05, 0x62, 0x79, 0x70, 0x56, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0d, 0x0a,
	0x05, 0x62, 0x79, 0x70, 0x56, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0d, 0x0a, 0x05,
	0x62, 0x79, 0x70, 0x56, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0d, 0x0a, 0x05, 0x62,
	0x79, 0x70, 0x56, 0x37, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0d, 0x0a, 0x05, 0x62, 0x79,
	0x70, 0x56, 0x38, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x12, 0x0d, 0x0a, 0x05, 0x62, 0x79, 0x70,
	0x56, 0x39, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0e, 0x0a, 0x06, 0x62, 0x79, 0x70, 0x56,
	0x31, 0x35, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x22, 0x74, 0x0a, 0x0e, 0x42, 0x79, 0x70, 0x53,
	0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0b, 0x62, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0d, 0x0a,
	0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x12, 0x22, 0x0a, 0x0a,
	0x6d, 0x73, 0x67, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x6d, 0x6d, 0x2e, 0x4d, 0x73, 0x67, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0c, 0x0a, 0x04, 0x75, 0x45, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x22, 0x7d,
	0x0a, 0x0f, 0x42, 0x79, 0x70, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1e, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6d, 0x6d, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x22, 0x0a, 0x0a, 0x6d, 0x73, 0x67, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6d, 0x6d, 0x2e, 0x4d, 0x73, 0x67, 0x45, 0x78,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0c, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x74, 0x65,
	0x6d, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6d, 0x6d,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x56, 0x0a,
	0x0c, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x0a, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x12, 0x12, 0x0a, 0x0a, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x32, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x74, 0x65,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6d, 0x6d, 0x2e, 0x53, 0x79, 0x6e,
	0x63, 0x49, 0x74, 0x65, 0x6d, 0x22, 0x85, 0x01, 0x0a, 0x08, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x22, 0x0a, 0x0a, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6d, 0x6d, 0x2e, 0x46, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x14, 0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x55, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x12, 0x0a, 0x0a,
	0x53, 0x79, 0x6e, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x1b, 0x0a, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x09, 0x2e, 0x6d, 0x6d, 0x2e, 0x45, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x06, 0x73, 0x79, 0x6e, 0x63, 0x56, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x22, 0xc9, 0x02,
	0x0a, 0x0a, 0x46, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x14, 0x0a, 0x0c,
	0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x56, 0x31, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x12, 0x2c, 0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63,
	0x56, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x6d, 0x2e, 0x53, 0x4b,
	0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x73,
	0x12, 0x2c, 0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x56, 0x33,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x6d, 0x2e, 0x53, 0x4b, 0x42, 0x75,
	0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x73, 0x12, 0x14,
	0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x56, 0x34, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x12, 0x2c, 0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79,
	0x6e, 0x63, 0x56, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x6d, 0x2e,
	0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x73, 0x12, 0x14, 0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63,
	0x56, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x12, 0x14, 0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x56, 0x37, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x12, 0x14,
	0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x56, 0x39, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x04, 0x12, 0x15, 0x0a, 0x0d, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79,
	0x6e, 0x63, 0x56, 0x31, 0x30, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x12, 0x15, 0x0a, 0x0d, 0x66,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63, 0x56, 0x31, 0x32, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x04, 0x12, 0x15, 0x0a, 0x0d, 0x66, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x79, 0x6e, 0x63,
	0x56, 0x31, 0x33, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x22, 0x24, 0x0a, 0x12, 0x53, 0x4b, 0x42,
	0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x73, 0x12,
	0x0e, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0x5a, 0x05, 0x2e, 0x2e, 0x2f, 0x6d, 0x6d,
}

var (
	file_finderMsg_proto_rawDescOnce sync.Once
	file_finderMsg_proto_rawDescData = file_finderMsg_proto_rawDesc
)

func file_finderMsg_proto_rawDescGZIP() []byte {
	file_finderMsg_proto_rawDescOnce.Do(func() {
		file_finderMsg_proto_rawDescData = protoimpl.X.CompressGZIP(file_finderMsg_proto_rawDescData)
	})
	return file_finderMsg_proto_rawDescData
}

var file_finderMsg_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_finderMsg_proto_goTypes = []interface{}{
	(*FinderGetMsgSessionIdRequest)(nil),  // 0: mm.FinderGetMsgSessionIdRequest
	(*RespV)(nil),                         // 1: mm.RespV
	(*Response)(nil),                      // 2: mm.Response
	(*FinderGetMsgSessionIdResponse)(nil), // 3: mm.FinderGetMsgSessionIdResponse
	(*FinderMsgSessionInfo)(nil),          // 4: mm.FinderMsgSessionInfo
	(*FinderGetContactListReq)(nil),       // 5: mm.FinderGetContactListReq
	(*FinderGetContactListResp)(nil),      // 6: mm.FinderGetContactListResp
	(*ContactList)(nil),                   // 7: mm.ContactList
	(*Region)(nil),                        // 8: mm.Region
	(*BypSendRequest)(nil),                // 9: mm.BypSendRequest
	(*BypSendResponse)(nil),               // 10: mm.BypSendResponse
	(*MsgExtInfo)(nil),                    // 11: mm.MsgExtInfo
	(*EInfo)(nil),                         // 12: mm.EInfo
	(*BypMsgPack)(nil),                    // 13: mm.BypMsgPack
	(*BypMsgVideoPack)(nil),               // 14: mm.BypMsgVideoPack
	(*BypMsgImgPack)(nil),                 // 15: mm.BypMsgImgPack
	(*BypSyncRequest)(nil),                // 16: mm.BypSyncRequest
	(*BypSyncResponse)(nil),               // 17: mm.BypSyncResponse
	(*SyncItemList)(nil),                  // 18: mm.SyncItemList
	(*SyncItem)(nil),                      // 19: mm.SyncItem
	(*FinderSync)(nil),                    // 20: mm.FinderSync
	(*SKBuiltinStringTs)(nil),             // 21: mm.SKBuiltinString_ts
	(*FinderBaseRequest)(nil),             // 22: FinderBaseRequest
	(*BaseRequest)(nil),                   // 23: BaseRequest
}
var file_finderMsg_proto_depIdxs = []int32{
	22, // 0: mm.FinderGetMsgSessionIdRequest.finderBaseRequest:type_name -> FinderBaseRequest
	21, // 1: mm.Response.sk:type_name -> mm.SKBuiltinString_ts
	1,  // 2: mm.FinderGetMsgSessionIdResponse.resp:type_name -> mm.RespV
	4,  // 3: mm.FinderGetMsgSessionIdResponse.finderMsgSessionInfo:type_name -> mm.FinderMsgSessionInfo
	22, // 4: mm.FinderGetContactListReq.finderBaseRequest:type_name -> FinderBaseRequest
	1,  // 5: mm.FinderGetContactListResp.resp:type_name -> mm.RespV
	8,  // 6: mm.ContactList.region:type_name -> mm.Region
	23, // 7: mm.BypSendRequest.baseRequest:type_name -> BaseRequest
	13, // 8: mm.BypSendRequest.bypMsgPack:type_name -> mm.BypMsgPack
	2,  // 9: mm.BypSendResponse.response:type_name -> mm.Response
	12, // 10: mm.MsgExtInfo.info:type_name -> mm.EInfo
	21, // 11: mm.BypMsgPack.sk:type_name -> mm.SKBuiltinString_ts
	15, // 12: mm.BypMsgPack.skImg:type_name -> mm.BypMsgImgPack
	14, // 13: mm.BypMsgPack.skVideo:type_name -> mm.BypMsgVideoPack
	23, // 14: mm.BypSyncRequest.baseRequest:type_name -> BaseRequest
	11, // 15: mm.BypSyncRequest.msgExtInfo:type_name -> mm.MsgExtInfo
	2,  // 16: mm.BypSyncResponse.response:type_name -> mm.Response
	11, // 17: mm.BypSyncResponse.msgExtInfo:type_name -> mm.MsgExtInfo
	18, // 18: mm.BypSyncResponse.syncItemList:type_name -> mm.SyncItemList
	19, // 19: mm.SyncItemList.syncItem:type_name -> mm.SyncItem
	20, // 20: mm.SyncItem.finderSync:type_name -> mm.FinderSync
	12, // 21: mm.SyncItem.syncInfo:type_name -> mm.EInfo
	21, // 22: mm.FinderSync.finderSyncV2:type_name -> mm.SKBuiltinString_ts
	21, // 23: mm.FinderSync.finderSyncV3:type_name -> mm.SKBuiltinString_ts
	21, // 24: mm.FinderSync.finderSyncV5:type_name -> mm.SKBuiltinString_ts
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_finderMsg_proto_init() }
func file_finderMsg_proto_init() {
	if File_finderMsg_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_finderMsg_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinderGetMsgSessionIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinderGetContactListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinderGetContactListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContactList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Region); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BypSendRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgExtInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BypMsgPack); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BypMsgVideoPack); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BypMsgImgPack); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BypSyncRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BypSyncResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncItemList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_finderMsg_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinderSync); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_finderMsg_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_finderMsg_proto_goTypes,
		DependencyIndexes: file_finderMsg_proto_depIdxs,
		MessageInfos:      file_finderMsg_proto_msgTypes,
	}.Build()
	File_finderMsg_proto = out.File
	file_finderMsg_proto_rawDesc = nil
	file_finderMsg_proto_goTypes = nil
	file_finderMsg_proto_depIdxs = nil
}
