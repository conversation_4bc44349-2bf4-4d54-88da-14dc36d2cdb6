package main

import (
	"fmt"
	"wechatdll/comm"
	"wechatdll/models/Friend"
	_ "wechatdll/routers"

	"github.com/astaxie/beego"
)

func main() {
	// 初始化好友关系检测日志系统
	Friend.InitFriendRelationLog()

	comm.RedisInitialize()
	_, err := comm.RedisClient.Ping().Result()
	if err != nil {
		panic(fmt.Sprintf("【Redis】连接失败，ERROR：%v", err.Error()))
	}
	beego.BConfig.WebConfig.DirectoryIndex = true
	beego.BConfig.WebConfig.StaticDir["/"] = "swagger"
	beego.SetLogFuncCall(false)
	//自定义错误页面
	beego.Run()
}
