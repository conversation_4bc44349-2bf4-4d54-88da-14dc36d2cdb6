package models

import (
	"fmt"
	"github.com/robfig/cron/v3"
	log "github.com/sirupsen/logrus"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"wechatdll/Cilent/mm"
)

type ResponseResult struct {
	Code    int64
	Success bool
	Message string
	Data    interface{}
	Debug   string
}
type ResponseResult2 struct {
	Code     int64
	Success  bool
	Message  string
	Data     interface{}
	Data62   string
	DeviceId string
}

type Task struct {
	ID    cron.EntryID
	Error string
}

/***
获取微信html页面
*/
func GetHTML(url string, headList []*mm.HttpHeader) (string, []string) {
	ua := "Mozilla/5.0 (Android8.1.0) AppleWebKit/537. 36 (KHTML, like Gecko) Chrome/41. 0.2225.0 Safari/537. 36"
	Language := "zh-cn,zh"
	client := &http.Client{}
	reqest, _ := http.NewRequest("GET", url, nil)
	reqest.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	reqest.Header.Set("Accept-Charset", "utf-8;q=0.7,*;q=0.3")
	//reqest.Header.Set("Accept-Encoding", "gzip, default")//这个有乱码，估计是没有解密，或解压缩
	reqest.Header.Set("Accept-Encoding", "utf-8") //这就没有乱码了
	reqest.Header.Set("Accept-Language", Language+";q=0.8,en-us;q=0.5,en;q=0.3")
	reqest.Header.Set("Cache-Control", "max-age=0")
	reqest.Header.Set("Connection", "keep-alive")
	reqest.Header.Set("Host", url)
	reqest.Header.Set("User-Agent", ua)
	for _, head := range headList {
		reqest.Header.Add(head.GetKey(), head.GetValue())
	}
	response, _ := client.Do(reqest)
	if response.StatusCode == 200 {
		body, _ := ioutil.ReadAll(response.Body)
		bodystr := string(body)
		v := response.Header
		b := v["Set-Cookie"]
		return bodystr, b //response.Header..Get("Set-Cookie")
	}
	return "", nil
}

func HttpPost(urls string, data url.Values, cookie []string) string {
	client := &http.Client{}
	retest, err := http.NewRequest("POST", urls, strings.NewReader(data.Encode()))
	if err != nil {
		log.Error("Http Post NewRequest出错!")
	}
	ua := "Mozilla/5.0 (Android8.1.0) AppleWebKit/537. 36 (KHTML, like Gecko) Chrome/41. 0.2225.0 Safari/537. 36"
	str := strings.Replace(strings.Trim(fmt.Sprint(cookie), "[]"), "HttpOnly", "", -1)
	retest.Header.Add("Cookie", str)
	retest.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	retest.Header.Add("User-Agent", ua)
	resp, err := client.Do(retest)
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Error("Http Post请求出错!")
	}
	return string(body)
}
