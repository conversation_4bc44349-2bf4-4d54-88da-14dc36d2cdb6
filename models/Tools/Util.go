package Tools

type UploadParam struct {
	Wxid   string
	Base64 string
}

type DownloadVoiceParam struct {
	Wxid         string
	FromUserName string
	MsgId        string
	Bufid        string
	Length       int
}

type DownloadData struct {
	Base64 []byte
	Length uint32
}

type DownloadParam struct {
	Wxid         string
	ToWxid       string
	MsgId        uint32
	DataLen      int
	Section      Section
	CompressType int
}

type DownloadAppAttachParam struct {
	Wxid     string
	AppID    string
	AttachId string
	UserName string
	DataLen  int
	Section  Section
}

type Section struct {
	StartPos uint32
	DataLen  uint32
}

type GetCertParam struct {
	Wxid    string
	Version uint32
}

type DownLoadEmojiParam struct {
	Wxid string
	Md5  string
}

type ThirdAppGrantParam struct {
	Wxid  string
	Appid string
	Url   string
}
