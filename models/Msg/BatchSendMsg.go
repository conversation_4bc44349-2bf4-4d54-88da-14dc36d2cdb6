package Msg

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"strconv"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/lib"
	"wechatdll/models"
)

type BatchSendMsgParam struct {
	Wxid    string
	ToWxids string
	Content string
}

func BatchSendMsg(Data BatchSendMsgParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	var ToWxids []*mm.ListContent
	T := time.Now().Unix()

	TowxdsSplit := strings.Split(Data.ToWxids, ",")

	for _, v := range TowxdsSplit {
		ClientMsgId, _ := strconv.ParseUint(lib.RandInt(10), 10, 64)
		time.Sleep(200 * time.Millisecond)
		ToWxids = append(ToWxids, &mm.ListContent{
			Toid: &mm.SKBuiltinStringT{
				String_: proto.String(v),
			},
			Content:     proto.String(Data.Content),
			Type:        proto.Int64(1),
			Utc:         proto.Int64(T),
			ClientMsgId: proto.Uint64(ClientMsgId),
			MsgSource:   proto.String("<msgsource><bizflag>0</bizflag></msgsource>"),
			Sign:        proto.String(""),
		})
	}

	req := &mm.BatchSendMsgRequest{
		Count: proto.Uint32(uint32(len(ToWxids))),
		List:  ToWxids,
	}

	//序列化
	reqdata, _ := proto.Marshal(req)

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/newsendmsg",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              522,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	NewSendMsgRespone := mm.BatchSendMsgRespone{}
	err = proto.Unmarshal(protobufdata, &NewSendMsgRespone)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    NewSendMsgRespone,
	}

}
