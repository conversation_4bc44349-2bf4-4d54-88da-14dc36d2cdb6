package Finder

import (
	"encoding/base64"
	"fmt"
	"github.com/golang/protobuf/proto"
	"strconv"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

func TargetUserPage(Data TargetUserPageParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	T := time.Now().Unix()

	LastBuffer := []byte{}

	if Data.LastBuffer != "" && Data.LastBuffer != "string" {
		key, _ := base64.StdEncoding.DecodeString(Data.LastBuffer)
		LastBuffer = key
	}

	req := &mm.FinderUserPageRequest{
		Username:      proto.String(Data.Target),
		MaxId:         proto.Uint64(0),
		FirstPageMd5:  proto.String(""),
		NeedFansCount: proto.Int(0),
		FinderBasereq: &mm.FinderBaseRequest{
			Userver: proto.Int(12),
			Scene:   proto.Int(12),
			ExptFla: proto.Uint32(499),
			CtxInfo: &mm.ClientContextInfo{
				ContextId: proto.String(fmt.Sprintf("1-1-20-c6e289fdc91e6cd085efe4b0d37acca6%v", time.Now().Unix())),
				//ContextId:            proto.String(fmt.Sprintf("Finder_Enter%v",T)),
				ClickTabContextId: proto.String(fmt.Sprintf("3-%v", T)),
				//ClientReportBuff:     proto.String(fmt.Sprintf(`{"sessionId":"143_%v#1900580_%v|$2_%v#"}`,T,T,T)),
				ClientReportBuff: proto.String(fmt.Sprintf(`{"enterSourceInfo":"{"finderusername":"","feedid":""}","sessionId":"143_%v#1900580_%v|$2_%v#","extrainfo":""}`, time.Now().Unix(), time.Now().Unix(), time.Now().Unix())),
			},
			Times: proto.Uint64(uint64(time.Now().Unix())),
		},
		LastBuffer: LastBuffer,
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/finderuserpage",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              3736,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := mm.FinderUserPageResponse{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}
	for _, v := range Response.Object {
		b := strconv.FormatUint(v.GetId(), 10)
		v.FinderObjectID = &b
	}
	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}
}
