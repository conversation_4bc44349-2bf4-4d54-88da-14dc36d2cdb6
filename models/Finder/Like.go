package Finder

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

func Like(Data LikeParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	T := time.Now().UnixNano() / 1e6

	req := &mm.FinderLikeRequest{
		Objectid:       proto.Uint64(Data.Id),
		Optype:         proto.Uint64(3),
		Likeid:         proto.Uint64(uint64(T)),
		FinderUsername: proto.String(Data.FinderUsername),
		Scene:          proto.Uint32(2),
		ObjectNonceId:  proto.String(Data.ObjectNonceId),
		FinderBasereq: &mm.FinderBaseRequest{
			Userver: proto.Int(12),
			Scene:   proto.Int(20),
			ExptFla: proto.Uint32(1),
			/*CtxInfo:              &mm.ClientContextInfo{
				ContextId:            proto.String(fmt.Sprintf("Finder_Enter%v",T)),
				ClickTabContextId:    proto.String(fmt.Sprintf("4-%v",T)),
				ClientReportBuff:     proto.String(fmt.Sprintf(`{"sessionId":"143_%v#1900580_%v|$2_%v#"}`,T,T,T)),
			},*/
			CtxInfo: &mm.ClientContextInfo{
				ContextId:         proto.String(fmt.Sprintf("1-1-18-c6e289fdc91e6cd085efe4b0d37acca6%v", time.Now().Unix())),
				ClickTabContextId: proto.String(fmt.Sprintf("1-%v", T)),
				ClientReportBuff:  proto.String(fmt.Sprintf(`{"enterSourceInfo":"{"finderusername":"","feedid":""}","sessionId":"143_%v#1900580_%v|$2_%v#","extrainfo":""}`, time.Now().Unix(), time.Now().Unix(), time.Now().Unix())),
			},
			Times: proto.Uint64(uint64(time.Now().Unix())),
		},
		SessionBuffer: proto.String(Data.SessionBuffer),
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/finderlike",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              3710,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := mm.FinderLikeResponse{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}
}
