package Finder

type DefaultParam struct {
	Wxid           string
	Value          string
	FinderUsername string
}

type TargetUserPageParam struct {
	Wxid       string
	Target     string
	LastBuffer string
}
type FinderSearchParam struct {
	Wxid string
}

type FinderGetTopicListParam struct {
	Wxid       string
	LastBuffer string
	TopTitle   string
}

type GetCommentDetailParam struct {
	Id             uint64
	LastBuffer     string
	FinderUsername string
	ObjectNonceId  string
	Wxid           string
}

type FinderLiveDetailParam struct {
	FinderObjectID uint64
	FinderNonceID  string
	Wxid           string
}

type LikeParam struct {
	Id             uint64
	FinderUsername string
	ObjectNonceId  string
	SessionBuffer  string
	Wxid           string
}

type CommentParam struct {
	Id            uint64
	Content       string
	Username      string
	ObjectNonceId string
	SessionBuffer string
	Wxid          string
}
type GetRecommendParam struct {
	Wxid string
}
type GetDetailsParam struct {
	Wxid           string
	LastBuffer     string
	Id             uint64
	FinderUsername string
	ObjectNonceId  string
}

type FinderSendTextParam struct {
	Wxid           string
	Text           string
	FinderUsername string
}
