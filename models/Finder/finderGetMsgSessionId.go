package Finder

import (
	"fmt"
	"github.com/gogo/protobuf/proto"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
)

//发送文字私信
func FinderGetMsgSessionId(wxId string, finderUsername string) (error, *mm.FinderGetMsgSessionIdResponse) {
	D, err := comm.GetLoginata(wxId)
	if err != nil {
		return err, nil
	}
	T := time.Now().UnixNano() / 1e6
	req := &mm.FinderGetMsgSessionIdRequest{
		MyAccountType:  proto.Uint64(2),
		FinderUsername: proto.String(finderUsername),
		FinderBaseRequest: &mm.FinderBaseRequest{
			Userver: proto.Int(12),
			ExptFla: proto.Uint32(499),
			CtxInfo: &mm.ClientContextInfo{
				ContextId:         proto.String(fmt.Sprintf("1-1-17-c6e289fdc91e6cd085efe4b0d37acca6%v", time.Now().Unix())),
				ClickTabContextId: proto.String(fmt.Sprintf("3-%v", T)),
				ClientReportBuff:  proto.String(fmt.Sprintf(`{"enterSourceInfo":"{"finderusername":"","feedid":""}","sessionId":"143_%v#1900580_%v|$2_%v#","extrainfo":""}`, time.Now().Unix(), time.Now().Unix(), time.Now().Unix())),
			},
			Times: proto.Uint64(uint64(time.Now().Unix())),
		},
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return err, nil
	}

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, _, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/findergetmsgsessionid",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              3828,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return err, nil
	}

	//解包
	Response := mm.FinderGetMsgSessionIdResponse{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return err, nil
	}
	return nil, &Response
}
