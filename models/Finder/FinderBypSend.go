package Finder

import (
	"fmt"
	"github.com/gogo/protobuf/proto"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

//发送文字私信
func FinderSendText(Data FinderSendTextParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	err, v := FinderGetMsgSessionId(Data.Wxid, Data.FinderUsername)

	toUsername := Data.FinderUsername
	var sessionId = v.FinderMsgSessionInfo.GetSessionId()
	msgSessionId := fmt.Sprintf("38c86c127559fed3422e3e14395ba617%v1_%v", sessionId, time.Now().Unix())
	req := &mm.BypSendRequest{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    []byte{},
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(0),
		},
		BizType: proto.Uint64(1),
		BypMsgPack: &mm.BypMsgPack{
			MsgType:      proto.Uint64(1),
			FromUsername: proto.String(Data.Wxid),
			ToUsername:   proto.String(toUsername),
			MsgSessionId: &msgSessionId,
			MsgSource:    proto.String(""),
			V6:           proto.String(""),
			SessionId:    &sessionId,
			Sk: &mm.SKBuiltinStringTs{
				String_: proto.String(Data.Text),
			},
		},
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/bypsend",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              3862,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := mm.BypSendResp{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}
	//失败 CLrl8////////wESAgoA
	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}
}
