package Finder

import (
	"encoding/base64"
	"fmt"
	"github.com/gogo/protobuf/proto"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/gg"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

func FinderGetTopicList(Data FinderGetTopicListParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}
	T := time.Now().Unix()

	LastBuffer := []byte{}

	if Data.LastBuffer != "" && Data.LastBuffer != "string" {
		key, _ := base64.StdEncoding.DecodeString(Data.LastBuffer)
		LastBuffer = key
	}

	req := &mm.FinderGetTopicListRequest{
		LastBuffer: LastBuffer,
		FinderBaseRequest: &mm.FinderBaseRequest{
			Userver: proto.Int(12),
			ExptFla: proto.Uint32(499),
			//ExptFla:              proto.Uint32(19),
			CtxInfo: &mm.ClientContextInfo{
				ContextId:         proto.String(fmt.Sprintf("1-1-17-c6e289fdc91e6cd085efe4b0d37acca6%v", time.Now().Unix())),
				ClickTabContextId: proto.String(fmt.Sprintf("3-%v", T)),
				ClientReportBuff:  proto.String(fmt.Sprintf(`{"enterSourceInfo":"{"finderusername":"","feedid":""}","sessionId":"143_%v#1900580_%v|$2_%v#","extrainfo":""}`, time.Now().Unix(), time.Now().Unix(), time.Now().Unix())),
			},
			Times: proto.Uint64(uint64(time.Now().Unix())),
		},
		TopicType:    proto.Uint64(1),
		Topic:        proto.String(Data.TopTitle),
		Latitude:     proto.Uint64(0x00000000),
		Longitude:    proto.Uint64(0x00000000),
		FromObjectId: proto.Uint64(13553527938065238112),
		V9:           proto.Uint64(0),
		V12:          proto.Uint64(0),
		V14:          proto.Uint64(0),
		V15:          proto.String(""),
		V16:          proto.String(""),
		V17:          proto.Uint64(0),
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/findergettopiclist",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              817,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := gg.FinderGetTopicListResponse{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}
	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}
}
