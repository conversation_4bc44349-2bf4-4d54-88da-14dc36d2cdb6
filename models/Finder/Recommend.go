package Finder

import (
	"encoding/base64"
	"fmt"
	"github.com/gogo/protobuf/proto"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/gg"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

func GetDetails(Data GetDetailsParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	T := time.Now().UnixNano() / 1e6

	LastBuffer := []byte{}

	if Data.LastBuffer != "" && Data.LastBuffer != "string" {
		key, _ := base64.StdEncoding.DecodeString(Data.LastBuffer)
		LastBuffer = key
	}

	req := &mm.FinderGetCommentDetailRequest{
		Objectid:       proto.Uint64(Data.Id),
		NeedObject:     proto.Uint32(1),
		LastBuffer:     LastBuffer,
		FinderUsername: proto.String(Data.FinderUsername),
		RefCommentId:   proto.Uint64(0),
		Scene:          proto.Uint32(20),
		Direction:      proto.Uint32(2),
		ObjectNonceId:  proto.String(Data.ObjectNonceId),
		FinderBasereq: &mm.FinderBaseRequest{
			Userver: proto.Int(12),
			ExptFla: proto.Uint32(499),
			CtxInfo: &mm.ClientContextInfo{
				ContextId:         proto.String(fmt.Sprintf("1-1-20-c6e289fdc91e6cd085efe4b0d37acca6%v", time.Now().Unix())),
				ClickTabContextId: proto.String(fmt.Sprintf("4-%v", T)),
				ClientReportBuff:  proto.String(fmt.Sprintf(`{"enterSourceInfo":"{"finderusername":"","feedid":""}","sessionId":"143_%v#1900580_%v|$2_%v#","extrainfo":""}`, time.Now().Unix(), time.Now().Unix(), time.Now().Unix())),
			},
			Times: proto.Uint64(uint64(time.Now().Unix())),
		},
		IdentityScene: proto.Uint32(2),
		Scene15:       proto.Uint32(2),
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/findergetcommentdetail",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              3763,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := gg.FinderGetCommentDetailRsp{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}
}
