package Finder

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

func Comment(Data CommentParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	T := time.Now().UnixNano() / 1e6

	req := &mm.FinderCommentRequest{
		Username:       proto.String(Data.Username),
		Objectid:       proto.Uint64(Data.Id),
		Content:        proto.String(Data.Content),
		ReplyCommentId: proto.Uint64(0),
		ReplyUsername:  proto.String(""),
		Optype:         proto.Uint32(0),
		Clientid:       proto.String(fmt.Sprintf("%v", T)),
		RootCommentId:  proto.Uint64(0),
		Scene:          proto.Uint32(2),
		ObjectNonceId:  proto.String(Data.ObjectNonceId),
		FinderBasereq: &mm.FinderBaseRequest{
			Userver: proto.Int(12),
			Scene:   proto.Int(1),
			ExptFla: proto.Uint32(499),
			CtxInfo: &mm.ClientContextInfo{
				ContextId:         proto.String(fmt.Sprintf("14-2-32-c6e289fdc91e6cd085efe4b0d37acca6%v", time.Now().Unix())),
				ClickTabContextId: proto.String(fmt.Sprintf("3-%v", T)),
				ClientReportBuff:  proto.String(fmt.Sprintf(`{"enterSourceInfo":"{"finderusername":"","feedid":""}","sessionId":"101_%v#$0_%v#","extrainfo":""}`, time.Now().Unix(), time.Now().Unix())),
			},
			Times: proto.Uint64(uint64(time.Now().Unix())),
		},
		SessionBuffer: proto.String(Data.SessionBuffer),
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/findercomment",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              3906,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := mm.FinderCommentResponse{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}
}
