package Label

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/tidwall/gjson"
	"strconv"
	"strings"
	"wechatdll/Cilent/mm"
	"wechatdll/models"
	"wechatdll/models/Msg"
)

func GetWxids(Data DeleteParam) models.ResponseResult {
	//同步消息
	Synckey := ""
	var GetLabelWxidsResponse *mm.GetLabelWxidsResponse
	var GetLabelWxidsInfo []*mm.GetLabelWxidsInfo
	Success := true
	Ret := 0
	for {
		Sync := Msg.Sync(Msg.SyncParam{
			Wxid:    Data.Wxid,
			Synckey: Synckey,
			Scene:   1,
		})

		if Sync.Success == false {
			Ret = -12
			Success = false
			break
		}

		SyncResult, _ := json.Marshal(Sync.Data)
		Synckey = gjson.Get(string(SyncResult), "KeyBuf.buffer").String()
		ModContacts := gjson.Get(string(SyncResult), "ModContacts").Array()
		if len(ModContacts) == 0 {
			break
		}

		for _, v := range ModContacts {
			if strings.Index(v.Get("WeiDianInfo").String(), Data.LabelID) != -1 {
				GetLabelWxidsInfo = append(GetLabelWxidsInfo, &mm.GetLabelWxidsInfo{
					UserName: proto.String(v.Get("UserName.string").String()),
					NickName: proto.String(v.Get("NickName.string").String()),
				})
			}
		}
	}

	LabelID, _ := strconv.ParseInt(Data.LabelID, 10, 64)

	GetLabelWxidsResponse = &mm.GetLabelWxidsResponse{
		BaseResponse: &mm.BaseResponse{
			Ret:    proto.Int(Ret),
			ErrMsg: &mm.SKBuiltinStringT{},
		},
		LabelID: proto.Uint32(uint32(LabelID)),
		Wxids:   GetLabelWxidsInfo,
	}

	if Success == false {
		return models.ResponseResult{
			Code:    -8,
			Success: true,
			Message: "请求失败",
			Data:    GetLabelWxidsResponse,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "请求成功",
		Data:    GetLabelWxidsResponse,
	}
}
