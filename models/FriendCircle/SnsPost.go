package FriendCircle

import (
	"encoding/hex"
	"fmt"
	"github.com/golang/protobuf/proto"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

type Messagearameter struct {
	Wxid         string
	Content      string
	BlackList    string
	WithUserList string
	ISVideo      bool
}

func Messages(Data Messagearameter) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}
	var BS []*mm.SKBuiltinStringT

	if Data.BlackList != "" && Data.BlackList != "string" {
		B := strings.Split(Data.BlackList, ",")
		if len(B) >= 1 {
			for _, v := range B {
				BS = append(BS, &mm.SKBuiltinStringT{
					String_: proto.String(v),
				})
			}
		}
	}

	var WS []*mm.SKBuiltinStringT

	if Data.WithUserList != "" && Data.WithUserList != "string" {
		W := strings.Split(Data.WithUserList, ",")
		if len(W) >= 1 {
			for _, v := range W {
				WS = append(WS, &mm.SKBuiltinStringT{
					String_: proto.String(v),
				})
			}
		}
	}

	T := time.Now().Unix()
	WCExtInfoseq := Algorithm.GetIOSExtSpamInfoAndDeviceToken(D.Wxid, D.Deviceid_str, D.DeviceName, D.DeviceToken, T)

	var PostBGImgType uint32
	Pictures := uint32(strings.Count(Data.Content, "<media>"))

	if strings.Count(Data.Content, "<media>") >= 1 {
		PostBGImgType = 1
	}

	req := &mm.SnsPostRequest{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    D.Sessionkey,
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(0),
		},
		ObjectDesc: &mm.SKBuiltinString_S{
			ILen:   proto.Uint32(uint32(len(Data.Content))),
			Buffer: proto.String(Data.Content),
		},
		WithUserListNum: proto.Uint32(uint32(len(WS))),
		Privacy:         proto.Uint32(0),
		SyncFlag:        proto.Uint32(0),
		ClientId:        proto.String(fmt.Sprintf("sns_post_%v_%v_0", D.Wxid, T)),
		PostBGImgType:   proto.Uint32(PostBGImgType),
		ObjectSource:    proto.Uint32(0),
		BlackListNum:    proto.Uint32(uint32(len(BS))),
		GroupUserNum:    proto.Uint32(0),
		SnsPostOperationFields: &mm.SnsPostOperationFields{
			ShareUrlOriginal: proto.String(""),
			ShareUrlOpen:     proto.String(""),
			JsAppid:          proto.String(""),
			ContactTagCount:  proto.Uint32(0),
			TempUserCount:    proto.Uint32(0),
		},
		PoiInfo: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(0),
			Buffer: []byte{},
		},
		MediaInfoCount: proto.Uint32(Pictures), //图片或视频数量
		ExtSpamInfo: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(WCExtInfoseq))),
			Buffer: WCExtInfoseq,
		},
	}

	if len(WS) >= 1 {
		req.WithUserList = WS
	}

	if len(BS) >= 1 {
		req.BlackList = BS
	}

	var MediaInfo []*mm.MediaInfo
	MediaType := mm.SnsMediaType_MMSNS_DATA_TEXT

	if Data.ISVideo == true {
		MediaType = mm.SnsMediaType_MMSNS_DATA_MUSIC
	}

	TT := (T - 10) * 1000

	if Pictures >= 1 {
		for i := 0; i < int(Pictures); i++ {
			MediaInfo = append(MediaInfo, &mm.MediaInfo{
				Source:    proto.Uint32(2),
				MediaType: &MediaType,
				SessionId: proto.String(fmt.Sprintf("memonts-%v", TT)),
				StartTime: proto.Uint32(uint32(T)),
			})
		}
		req.MediaInfo = MediaInfo
	}

	reqdata, err := proto.Marshal(req)

	fmt.Println(hex.EncodeToString(reqdata))

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/mmsnspost",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              209,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      true,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	SnsPostResponse := mm.SnsPostResponse{}
	err = proto.Unmarshal(protobufdata, &SnsPostResponse)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    SnsPostResponse,
	}

}
