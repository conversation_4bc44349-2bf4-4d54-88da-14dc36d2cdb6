package FriendCircle

import (
	"encoding/base64"
	"fmt"
	"github.com/golang/protobuf/proto"
	"strings"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

func SnsUploadSep(Data SnsUploadSepParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	var Bs64Data []byte
	Base64Data := strings.Split(Data.Base64, ",")

	if len(Base64Data) > 1 {
		Bs64Data, _ = base64.StdEncoding.DecodeString(Base64Data[1])
	} else {
		Bs64Data, _ = base64.StdEncoding.DecodeString(Data.Base64)
	}

	req := &mm.SnsUploadRequest{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    []byte{},
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(0),
		},
		Type:     proto.Uint32(Data.Type),
		StartPos: proto.Uint32(Data.StartPos),
		TotalLen: proto.Uint32(Data.TotalLen),
		Buffer: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Bs64Data))),
			Buffer: Bs64Data,
		},
		ClientId: proto.String(Data.ClientID),
		MD5:      proto.String(Data.HashMd5),
	}

	//序列化
	reqdata, _ := proto.Marshal(req)

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/mmsnsupload",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              207,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      true,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	SnsPostResponse := mm.SnsUploadResponse{}
	err = proto.Unmarshal(protobufdata, &SnsPostResponse)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    SnsPostResponse,
	}
}
