package Friend

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/Cilent/wechat"
	"wechatdll/comm"
	"wechatdll/models"

	"github.com/golang/protobuf/proto"
)

type FriendRelationParam struct {
	Wxid     string
	UserName string
}

func GetBaseRequest(D *comm.LoginData) *mm.BaseRequest {
	ret := &mm.BaseRequest{}
	ret.SessionKey = []byte(D.Sessionkey)
	ret.Uin = &D.Uin
	if !strings.HasPrefix(D.Deviceid_str, "A") {
		ret.DeviceId = D.Deviceid_byte
		ret.ClientVersion = proto.Int32(int32(D.ClientVersion))
		ret.Scene = proto.Uint32(0)
		//log.Info("ios is base request")
	} else {
		ret.ClientVersion = proto.Int32(int32(D.ClientVersion))
		ret.DeviceId = D.Deviceid_byte
		ret.Scene = proto.Uint32(1)
	}
	return ret
}

// 创建日志文件
func createLogFile() *os.File {
	logFile, err := os.OpenFile("friend_relation_debug.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("无法创建日志文件: %v", err)
		return nil
	}
	return logFile
}

// 写入日志
func writeLog(message string) {
	logFile := createLogFile()
	if logFile != nil {
		defer logFile.Close()
		timestamp := time.Now().Format("2006-01-02 15:04:05")
		logFile.WriteString(fmt.Sprintf("[%s] %s\n", timestamp, message))
	}
	// 同时输出到控制台
	log.Printf("[好友关系检测] %s", message)
}

// 好友关系检测
func FriendRelation(Data FriendRelationParam) models.ResponseResult {
	writeLog(fmt.Sprintf("开始好友关系检测 - Wxid: %s, UserName: %s", Data.Wxid, Data.UserName))

	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		writeLog(fmt.Sprintf("获取登录数据失败: %v", err))
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	writeLog(fmt.Sprintf("成功获取登录数据 - Uin: %d, SessionKey长度: %d, DeviceId长度: %d",
		D.Uin, len(D.Sessionkey), len(D.Deviceid_byte)))

	//构建请求  MMBizJsApiGetUserOpenIdRequest  SearchContactRequest
	writeLog("开始构建请求数据")
	req := &wechat.MMBizJsApiGetUserOpenIdRequest{
		BaseRequest: &wechat.BaseRequest{
			SessionKey:    D.Sessionkey,
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(1),
		},
		AppId:    proto.String("wx7c8d593b2c3a7703"),
		UserName: proto.String(Data.UserName),
	}

	writeLog(fmt.Sprintf("请求参数 - AppId: %s, UserName: %s, Uin: %d, ClientVersion: %d",
		"wx7c8d593b2c3a7703", Data.UserName, D.Uin, D.ClientVersion))

	reqdata, err := proto.Marshal(req)

	if err != nil {
		writeLog(fmt.Sprintf("请求序列化失败: %v", err))
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	writeLog(fmt.Sprintf("请求序列化成功，数据长度: %d", len(reqdata)))
	//发包
	var Host = comm.GetIp(*D)
	writeLog(fmt.Sprintf("开始发送网络请求 - Host: %s, CGI: 1177", Host))
	writeLog(fmt.Sprintf("请求URL: %s", "/cgi-bin/mmbiz-bin/usrmsg/mmbizjsapi_getuseropenid"))

	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{

		Host:   Host,
		Cgiurl: "/cgi-bin/mmbiz-bin/usrmsg/mmbizjsapi_getuseropenid",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              1177,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      true,
		},
	}, D.MmtlsKey)

	if err != nil {
		writeLog(fmt.Sprintf("网络请求失败 - 错误类型: %d, 错误信息: %v", errtype, err))
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	writeLog(fmt.Sprintf("网络请求成功，响应数据长度: %d", len(protobufdata)))

	//解包
	writeLog("开始解析响应数据")
	Response := wechat.MMBizJsApiGetUserOpenIdResponse{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		writeLog(fmt.Sprintf("响应反序列化失败: %v", err))
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	writeLog("响应反序列化成功")

	// 详细记录响应内容
	if Response.BaseResponse != nil {
		writeLog(fmt.Sprintf("BaseResponse - Ret: %d, ErrMsg: %s",
			Response.BaseResponse.GetRet(), Response.BaseResponse.GetErrMsg().GetString_()))
	}

	writeLog(fmt.Sprintf("响应详情 - Openid: %s, NickName: %s, HeadImgUrl: %s, Sign: %s, FriendRelation: %d",
		Response.GetOpenid(), Response.GetNickName(), Response.GetHeadImgUrl(),
		Response.GetSign(), Response.GetFriendRelation()))

	//判断好友关系，1//删除 4/自己拉黑 5/被拉黑 0/正常
	writeLog(fmt.Sprintf("好友关系检测完成 - 关系状态: %d (1=删除, 4=自己拉黑, 5=被拉黑, 0=正常)", Response.GetFriendRelation()))

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}
}
