package Friend

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

type SendRequestParam struct {
	Wxid          string
	V1            string
	V2            string
	Scene         int
	VerifyContent string
	OpCode        int32
}

func SendRequest(Data SendRequestParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	VerifyUserList := make([]*mm.VerifyUser, 0)

	VerifyUserList = append(VerifyUserList, &mm.VerifyUser{
		Value:               proto.String(Data.V1),
		VerifyUserTicket:    proto.String(""),
		AntispamTicket:      proto.String(Data.V2),
		FriendFlag:          proto.Uint32(0),
		ChatRoomUserName:    proto.String(""),
		SourceUserName:      proto.String(""),
		SourceNickName:      proto.String(""),
		ScanQrcodeFromScene: proto.Uint32(0),
		ReportInfo:          proto.String(""),
		OuterUrl:            proto.String(""),
		SubScene:            proto.Int32(0),
	})

	T := time.Now().Unix()
	WCExtInfoseq := Algorithm.GetIOSExtSpamInfoAndDeviceToken(D.Wxid, D.Deviceid_str, D.DeviceName, D.DeviceToken, T)

	req := &mm.VerifyUserRequest{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    D.Sessionkey,
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(0),
		},
		Opcode:             proto.Int32(Data.OpCode),
		VerifyUserListSize: proto.Uint32(1),
		VerifyUserList:     VerifyUserList,
		VerifyContent:      proto.String(Data.VerifyContent),
		SceneList:          []byte{byte(Data.Scene)},
		SceneListCount:     proto.Uint32(1),
		ExtSpamInfo: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(WCExtInfoseq))),
			Buffer: WCExtInfoseq,
		},
		NeedConfirm: proto.Uint32(1),
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/verifyuser",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              137,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := mm.VerifyUserResponse{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}
}
