package Group

import (
	"encoding/base64"
	"fmt"
	"github.com/golang/protobuf/proto"
	"strings"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

func GroupList(Data GroupListParam) models.ResponseResult {

	list := make([]mm.ModContact, 0)
	v := int64(0)
	initGroup(Data, []byte(""), &list, v)
	return models.ResponseResult{
		Code:    0,
		Success: false,
		Message: fmt.Sprintf("请求成功"),
		Data:    list,
	}
}

func initGroup(Data GroupListParam, key []byte, list *[]mm.ModContact, v int64) {
	response, _ := GroupListReq(Data)
	i := len(*list)
	for _, item := range response.CmdList.List {
		if item.GetCmdId() == 2 || item.GetCmdId() == 5 {
			contact := new(mm.ModContact)
			err := proto.Unmarshal(item.GetCmdBuf().GetBuffer(), contact)
			if err != nil {
				continue
			}
			// 判断contact是否是群 == 0 不是群
			if contact.GetChatroomVersion() == 0 {
				continue
			}
			//被移除群聊
			if contact.GetChatRoomNotify() == 0 {
				fmt.Println("消息免打扰群, 群wxid = ", contact.GetUserName().GetString_(), " 群昵称：", contact.GetNickName().GetString_())
			} else {
				fmt.Println("微信群, 群wxid = ", contact.GetUserName().GetString_(), " 群昵称：", contact.GetNickName().GetString_())
			}
			userName := contact.GetUserName().GetString_()
			if strings.HasSuffix(userName, "@chatroom") {
				add := false
				if contact.NewChatroomData.GetMemberCount() > 0 {
					for _, v := range contact.NewChatroomData.ChatRoomMember {
						if Data.Wxid == v.GetUserName() {
							add = true
						}
					}
				}
				if add {
					*list = append(*list, *contact)
				}
			}
		}
	}
	v++
	if len(*list) > i || (len(*list) == 0 && v <= 1) {
		key = response.KeyBuf.Buffer
		Data.Key = base64.StdEncoding.EncodeToString(key)
		initGroup(Data, key, list, v)
	}
}
func GroupListReq(Data GroupListParam) (*mm.NewSyncResponse, error) {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return nil, err
	}

	var Synckey mm.SKBuiltinBufferT

	Synckey = mm.SKBuiltinBufferT{
		ILen:   proto.Uint32(uint32(len(D.SyncKey))),
		Buffer: D.SyncKey,
	}

	//if Data.Synckey != "" {
	//	key, _ := base64.StdEncoding.DecodeString(Data.Synckey)
	//	Synckey = mm.SKBuiltinBufferT{
	//		ILen:   proto.Uint32(uint32(len(key))),
	//		Buffer: key,
	//	}
	//}
	req := &mm.NewSyncRequest{
		Oplog: &mm.CmdList{
			Count: proto.Uint32(0),
			List:  nil,
		},
		Selector:      proto.Uint32(7),
		KeyBuf:        &Synckey,
		Scene:         proto.Uint32(3),
		DeviceType:    proto.String(D.DeviceType),
		SyncMsgDigest: proto.Uint32(1),
	}
	reqdata, err := proto.Marshal(req)

	if err != nil {
		return nil, err
	}
	Host := comm.GetIp(*D)
	//发包
	protobufdata, _, _, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/newsync",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              138,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			Serversessionkey: D.Serversessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return nil, err
	}

	//解包
	NewSyncResponse := mm.NewSyncResponse{}

	err = proto.Unmarshal(protobufdata, &NewSyncResponse)
	if err != nil {
		return nil, err
	}
	return &NewSyncResponse, err
}
