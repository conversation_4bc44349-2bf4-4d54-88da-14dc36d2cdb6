package Group

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

type ScanIntoGroupParam struct {
	Wxid string
	Url  string
}

func ScanIntoGroup(Data ScanIntoGroupParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	//组包
	req := &mm.GetA8KeyReq{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    D.Sessionkey,
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(0),
		},
		OpCode: proto.Uint32(2),
		A2Key: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(0),
			Buffer: []byte{},
		},
		AppID: &mm.SKBuiltinStringT{
			String_: proto.String(""),
		},
		Scope: &mm.SKBuiltinStringT{
			String_: proto.String(""),
		},
		State: &mm.SKBuiltinStringT{
			String_: proto.String(""),
		},
		ReqUrl: &mm.SKBuiltinStringT{
			String_: proto.String(Data.Url),
		},
		Scene:       proto.Uint32(4),
		BundleID:    proto.String(""),
		A2KeyNew:    []byte{},
		FontScale:   proto.Uint32(118),
		NetType:     proto.String("WIFI"),
		CodeType:    proto.Uint32(19),
		CodeVersion: proto.Uint32(5),
		RequestId:   proto.Uint64(uint64(time.Now().Unix())),
		OuterUrl:    proto.String(""),
		SubScene:    proto.Uint32(0),
	}

	//序列化
	reqdata, _ := proto.Marshal(req)

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/geta8key",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              233,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	GetA8KeyResp := mm.GetA8KeyResp{}
	err = proto.Unmarshal(protobufdata, &GetA8KeyResp)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	_, err = ScanIntoGrouppost(*GetA8KeyResp.FullURL)

	if err != nil {
		if strings.Index(err.Error(), "@chatroom") != -1 {
			return models.ResponseResult{
				Code:    0,
				Success: true,
				Message: "进群成功",
				Data:    err.Error(),
			}
		}
	}

	return models.ResponseResult{
		Code:    -8,
		Success: false,
		Message: "进群失败",
		Data:    nil,
	}

}

func ScanIntoGrouppost(URL string) (string, error) {

	var err error

	postValue := url.Values{
		"forBlackberry": {"forceToUsePost"},
	}
	req, err := http.PostForm(URL, postValue)

	if err != nil {
		return "", err
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Referer", URL)
	req.Header.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Add("User-Agent", "Mozilla/5.0 (iPad; CPU OS 13_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/7.0.12(0x17000c21) NetType/WIFI Language/zh_CN")
	defer req.Body.Close()
	body, err := ioutil.ReadAll(req.Body)
	if err != nil {
		return "", err
	}
	return string(body), nil
}
