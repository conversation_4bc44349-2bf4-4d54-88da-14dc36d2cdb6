package TenPay

import (
//"encoding/xml"
//"fmt"
//"github.com/golang/protobuf/proto"
//"net/url"
//"wechatdll/Algorithm"
//"wechatdll/Cilent/mm"
//"wechatdll/Xml"
//"wechatdll/bts"
//"wechatdll/comm"
//"wechatdll/models"
//"wechatdll/models/User"
)

//func TransferOperation(Data TransferOperationParam) models.ResponseResult {
//	D, err := comm.GetLoginata(Data.Wxid)
//	if err != nil {
//		return models.ResponseResult{
//			Code:    -8,
//			Success: false,
//			Message: fmt.Sprintf("异常：%v", err.Error()),
//			Data:    nil,
//		}
//	}
//
//	//解析xml
//	var TransferOperation Xml.TransferOperation
//	_ = xml.Unmarshal([]byte(Data.Xml), &TransferOperation)
//
//	//读取个人信息
//	WxInfo := User.GetContractProfile(Data.Wxid)
//
//	if WxInfo.Code != 0 {
//		return models.ResponseResult{
//			Code:    WxInfo.Code,
//			Success: false,
//			Message: fmt.Sprintf("个人信息获取异常：%v", WxInfo.Message),
//			Data:    WxInfo.Data,
//		}
//	}
//
//	Info := bts.GetProfile(WxInfo.Data)
//
//	City := Info.GetUserInfo().GetCity()
//	Province := Info.GetUserInfo().GetProvince()
//
//	Text := "city=" + City + "&invalid_time=" + TransferOperation.Appmsg.Wcpayinfo.Invalidtime + "&op=confirm&province=" + Province + "&trans_id=" + TransferOperation.Appmsg.Wcpayinfo.Transferid + "&username=" + Data.ToWxid
//	Sign, err := Algorithm.SingWith3DES(Text)
//
//	if err != nil {
//		return models.ResponseResult{
//			Code:    -8,
//			Success: false,
//			Message: fmt.Sprintf("数据加密异常：%v", err.Error()),
//			Data:    nil,
//		}
//	}
//
//	Text = Text + "&WCPaySign=" + Sign
//
//	encrypt_userinfo := url.QueryEscape(Algorithm.GetEncryptUserInfo(D.Deviceid_str, D.Aeskey))
//	encrypt_key := url.QueryEscape(Algorithm.GetEncryptKey(D.Aeskey))
//
//	TextWx := "encrypt_key=" + encrypt_key + "&encrypt_userinfo=" + encrypt_userinfo
//
//	req := &mm.TenPayRequest{
//		BaseRequest: &mm.BaseRequest{
//			SessionKey:    D.Sessionkey,
//			Uin:           proto.Uint32(D.Uin),
//			DeviceId:      D.Deviceid_byte,
//			ClientVersion: proto.Int32(int32(D.ClientVersion)),
//			DeviceType:    []byte(D.DeviceType),
//			Scene:         proto.Uint32(0),
//		},
//		CgiCmd:     proto.Uint32(85),
//		OutPutType: proto.Uint32(1),
//		ReqText: &mm.SKBuiltinBufferT{
//			ILen:   proto.Uint32(uint32(len([]byte(Text)))),
//			Buffer: []byte(Text),
//		},
//		ReqTextWx: &mm.SKBuiltinBufferT{
//			ILen:   proto.Uint32(uint32(len([]byte(TextWx)))),
//			Buffer: []byte(TextWx),
//		},
//	}
//
//	//序列化
//	reqdata, _ := proto.Marshal(req)
//
//	Host := comm.GetIp(*D)
//
//	//发包
//	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
//		Host:   Host,
//		Cgiurl: "/cgi-bin/mmpay-bin/transferoperation",
//		Proxy:  D.Proxy,
//		PackData: Algorithm.PackData{
//			Reqdata:          reqdata,
//			Cgi:              1691,
//			Uin:              D.Uin,
//			Cookie:           D.Cooike,
//			Sessionkey:       D.Sessionkey,
//			EncryptType:      5,
//			Loginecdhkey:     D.Loginecdhkey,
//			Clientsessionkey: D.Clientsessionkey,
//			UseCompress:      false,
//		},
//	}, D.MmtlsKey)
//
//	if err != nil {
//		return models.ResponseResult{
//			Code:    errtype,
//			Success: false,
//			Message: err.Error(),
//			Data:    nil,
//		}
//	}
//
//	//解包
//	Response := mm.TenPayResponse{}
//	err = proto.Unmarshal(protobufdata, &Response)
//	if err != nil {
//		return models.ResponseResult{
//			Code:    -8,
//			Success: false,
//			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
//			Data:    nil,
//		}
//	}
//
//	return models.ResponseResult{
//		Code:    0,
//		Success: true,
//		Message: "成功",
//		Data:    Response,
//	}
//}
