package Login

import (
	"fmt"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

func UpdateDeviceToken(Data UpdateDeviceTokenParam) models.ResponseResult {
	var Token mm.TrustResponse
	var err error

	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	//初始化Mmtls
	httpclient, _, err := comm.MmtlsInitialize(D.Proxy)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("MMTLS初始化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	if Data.Model == "android" {

	} else {
		Token, err = IPadGetDeviceToken(<PERSON><PERSON>_str, "iPad11,3", <PERSON><PERSON>, "iOS14.2", int32(Algorithm.IPadVersion), *httpclient, D.Proxy)
	}

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("DeviceToken异常：%v", err.Error()),
			Data:    nil,
		}
	}

	if Token.GetTrustResponseData().GetDeviceToken() == "" {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: "DeviceToken,获取失败。",
			Data:    Token,
		}
	}

	//更新缓存
	D.DeviceToken = Token
	err = comm.CreateLoginData(*D, D.Wxid, 0)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "更新成功",
		Data:    nil,
	}

}
