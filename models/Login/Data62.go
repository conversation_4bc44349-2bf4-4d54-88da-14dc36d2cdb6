package Login

import (
	"container/list"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"log"
	"os"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/lib"
	"wechatdll/models"

	"github.com/astaxie/beego"
	"github.com/golang/protobuf/proto"
)

func Data62(Data Data62LoginReq, domain string) models.ResponseResult {
	//初始化Mmtls
	httpclient, MmtlsClient, err := comm.MmtlsInitialize(Data.Proxy)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("MMTLS初始化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	devicelId := lib.Get62Key(Data.Data62)
	if devicelId[:2] != "49" {
		devicelId = "49" + devicelId[2:]
	}

	DeviceToken, err := IPadGetDeviceToken(devicelId, "iPad11,3", Data.DeviceName, "iOS14.2", int32(Algorithm.IPadVersion), *httpclient, Data.Proxy)
	if err != nil {
		DeviceToken = mm.TrustResponse{}
	}

	devicelIdByte, _ := hex.DecodeString(devicelId)

	prikey, pubkey := Algorithm.GetEcdh713Key()

	T := time.Now().Unix()

	Wcstf := Algorithm.IphoneWcstf07(Data.UserName)
	Wcste := Algorithm.IphoneWcste07(0, 0)

	aeskey := []byte(lib.RandSeq(16)) //获取随机密钥

	accountRequest := &mm.ManualAuthRsaReqData{
		RandomEncryKey: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(aeskey))),
			Buffer: aeskey,
		},
		CliPubEcdhkey: &mm.ECDHKey{
			Nid: proto.Int32(713),
			Key: &mm.SKBuiltinBufferT{
				ILen:   proto.Uint32(uint32(len(pubkey))),
				Buffer: pubkey,
			},
		},
		UserName: proto.String(Data.UserName),
		Pwd:      proto.String(lib.MD5ToLower(Data.Password)),
	}

	ccData := &mm.CryptoData{
		Version:     []byte("********"),
		Type:        proto.Uint32(1),
		EncryptData: Algorithm.GetiPhoneNewSpamData07(devicelId, Data.DeviceName, DeviceToken),
		Timestamp:   proto.Uint32(uint32(T)),
		Unknown5:    proto.Uint32(5),
		Unknown6:    proto.Uint32(0),
	}

	ccDataseq, _ := proto.Marshal(ccData)

	DeviceTokenCCD := &mm.DeviceToken{
		Version:   proto.String(""),
		Encrypted: proto.Uint32(1),
		Data: &mm.SKBuiltinStringT{
			String_: proto.String(DeviceToken.GetTrustResponseData().GetDeviceToken()),
		},
		TimeStamp: proto.Uint32(uint32(T)),
		Optype:    proto.Uint32(2),
		Uin:       proto.Uint32(0),
	}
	DeviceTokenCCDPB, _ := proto.Marshal(DeviceTokenCCD)

	WCExtInfo := &mm.WCExtInfo{
		Wcstf: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Wcstf))),
			Buffer: Wcstf,
		},
		Wcste: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Wcste))),
			Buffer: Wcste,
		},
		CcData: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(ccDataseq))),
			Buffer: ccDataseq,
		},
		DeviceToken: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(DeviceTokenCCDPB))),
			Buffer: DeviceTokenCCDPB,
		},
	}

	WCExtInfoseq, _ := proto.Marshal(WCExtInfo)

	Imei := Algorithm.IOSImei(devicelId)
	SoftType := Algorithm.SoftType_iPad(devicelId)
	ClientSeqId := fmt.Sprintf("%v_%v", devicelId, time.Now().Unix())
	uuid1, _ := Algorithm.IOSUuid(devicelId)

	DeviceType := []byte(Algorithm.IPadDeviceType)

	deviceRequest := &mm.ManualAuthAesReqData{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    []byte{},
			Uin:           proto.Uint32(0),
			DeviceId:      devicelIdByte,
			ClientVersion: proto.Int32(int32(Algorithm.IPadVersion)),
			DeviceType:    DeviceType,
			Scene:         proto.Uint32(1),
		},
		BaseReqInfo:  &mm.BaseAuthReqInfo{},
		Imei:         &Imei,
		SoftType:     &SoftType,
		BuiltinIpseq: proto.Uint32(0),
		ClientSeqId:  &ClientSeqId,
		DeviceName:   proto.String(Data.DeviceName),
		DeviceType:   proto.String(Algorithm.IPadDeviceType),
		Language:     proto.String("zh_CN"),
		TimeZone:     proto.String("8.0"),
		Channel:      proto.Int(0),
		TimeStamp:    proto.Uint32(uint32(T)),
		DeviceBrand:  proto.String("Apple"),
		RealCountry:  proto.String("CN"),
		BundleId:     proto.String("com.tencent.xin"),
		AdSource:     &uuid1,
		IphoneVer:    proto.String("iPad11,3"),
		InputType:    proto.Uint32(2),
		ExtSpamInfo: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(WCExtInfoseq))),
			Buffer: WCExtInfoseq,
		},
	}

	requset := &mm.SecManualLoginRequest{
		RsaReqData: accountRequest,
		AesReqData: deviceRequest,
	}

	reqdata, _ := proto.Marshal(requset)

	hec := &Algorithm.Client{}
	hec.Init("IOS")
	hecData := hec.HybridEcdhPackIosEn(252, 0, nil, reqdata, []byte(""))
	recvData, err := httpclient.MMtlsPost(domain, "/cgi-bin/micromsg-bin/secmanualauth", hecData, Data.Proxy)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	if len(recvData) <= 31 {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: "微信返回的数据存在异常",
			Data:    hex.EncodeToString(recvData),
		}
	}

	ph1 := hec.HybridEcdhPackIosUn(recvData)
	loginRes := mm.UnifyAuthResponse{}
	err = proto.Unmarshal(ph1.Data, &loginRes)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	ExecutionLog, _ := beego.AppConfig.Bool("ExecutionLog")

	if ExecutionLog {
		T := time.Now().Format("2006010215")
		logFile, _ := os.OpenFile(fmt.Sprintf("log/%v_login.txt", T), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0755)
		log.SetFlags(0)
		loger := log.New(logFile, "", log.Flags())
		if loginRes.GetBaseResponse().GetRet() != -301 && loginRes.GetBaseResponse().GetRet() != -305 {
			loger.Printf("%v----%v----%v----%v", Data.UserName, Data.Password, Data.Data62, Data.Proxy.ProxyIp)
		}
	}

	if loginRes.GetBaseResponse().GetRet() == 0 && loginRes.GetUnifyAuthSectFlag() > 0 {
		var LoginData comm.LoginData
		LoginData.Cooike = ph1.Cookies
		LoginData.MmtlsHost = domain
		LoginData.Deviceid_str = devicelId
		LoginData.Deviceid_byte = devicelIdByte
		LoginData.MmtlsKey = MmtlsClient
		LoginData.ClientVersion = Algorithm.IPadVersion
		LoginData.DeviceType = Algorithm.IPhoneDeviceType
		LoginData.Proxy = Data.Proxy

		Wx_loginecdhkey := Algorithm.DoECDH713Key(prikey, loginRes.GetAuthSectResp().GetSvrPubEcdhkey().GetKey().GetBuffer())
		Wx_loginecdhkeylen := int32(len(Wx_loginecdhkey))
		m := md5.New()
		m.Write(Wx_loginecdhkey[:Wx_loginecdhkeylen])
		ecdhdecrptkey := m.Sum(nil)
		LoginData.Loginecdhkey = Wx_loginecdhkey
		LoginData.Uin = loginRes.GetAuthSectResp().GetUin()
		LoginData.Wxid = loginRes.GetAcctSectResp().GetUserName()
		LoginData.Alais = loginRes.GetAcctSectResp().GetAlias()
		LoginData.Mobile = loginRes.GetAcctSectResp().GetBindMobile()
		LoginData.NickName = loginRes.GetAcctSectResp().GetNickName()
		LoginData.Email = loginRes.GetAcctSectResp().GetBindEmail()
		LoginData.Sessionkey = Algorithm.AesDecrypt(loginRes.GetAuthSectResp().GetSessionKey().GetBuffer(), ecdhdecrptkey)
		LoginData.Sessionkey_2 = loginRes.GetAuthSectResp().GetSessionKey().GetBuffer()
		LoginData.Autoauthkey = loginRes.GetAuthSectResp().GetAutoAuthKey().GetBuffer()
		LoginData.Autoauthkeylen = int32(loginRes.GetAuthSectResp().GetAutoAuthKey().GetILen())
		LoginData.Serversessionkey = loginRes.GetAuthSectResp().GetServerSessionKey().GetBuffer()
		LoginData.Clientsessionkey = loginRes.GetAuthSectResp().GetClientSessionKey().GetBuffer()
		LoginData.DeviceToken = DeviceToken

		var Dns []comm.Dns
		//记录DNSip
		ip_info := loginRes.GetNetworkSectResp().GetBuiltinIplist().GetShortConnectIplist()
		for _, v := range ip_info {
			IP := strings.Replace(v.GetIp(), string(byte(0x00)), "", -1)
			Host := strings.Replace(v.GetHost(), string(byte(0x00)), "", -1)
			if IP != "127.0.0.1" && Host != "localhost" && strings.Index(Host, "pay") == -1 {
				Dns = append(Dns, comm.Dns{
					Ip:   IP,
					Host: Host,
				})
			}
		}
		LoginData.Dns = Dns

		err := comm.CreateLoginData(LoginData, LoginData.Wxid, 0)

		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("系统异常：%v", err.Error()),
				Data:    nil,
			}
		}

		return models.ResponseResult{
			Code:    0,
			Success: true,
			Message: "成功",
			Data: CheckLoginRes{
				Uuid:     LoginData.Uuid,
				WxId:     LoginData.Wxid,
				NickName: LoginData.NickName,
				Status:   2,
				Device:   LoginData.DeviceName,
				HeadUrl:  LoginData.HeadUrl,
				Mobile:   LoginData.Mobile,
				Email:    LoginData.Email,
				Alias:    LoginData.Alais,
				Data62:   "",
			},
		}
	}

	//30系列转向
	if loginRes.GetBaseResponse().GetRet() == -301 {
		var Wx_newLong_Host, Wx_newshort_Host, Wx_newshortext_Host list.List

		dns_info := loginRes.GetNetworkSectResp().GetNewHostList().GetList()
		for _, v := range dns_info {
			if v.GetHost() == "long.weixin.qq.com" {
				ip_info := loginRes.GetNetworkSectResp().GetBuiltinIplist().GetLongConnectIplist()
				for _, ip := range ip_info {
					host := ip.GetHost()
					host = strings.Replace(host, string(byte(0x00)), "", -1)
					if host == v.GetRedirect() {
						Wx_newLong_Host.PushBack(host)
					}
				}
			} else if v.GetHost() == "short.weixin.qq.com" {
				ip_info := loginRes.GetNetworkSectResp().GetBuiltinIplist().GetShortConnectIplist()
				for _, ip := range ip_info {
					host := ip.GetHost()
					host = strings.Replace(host, string(byte(0x00)), "", -1)
					if host == v.GetRedirect() {
						Wx_newshort_Host.PushBack(host)
					}
				}
			} else if v.GetHost() == "extshort.weixin.qq.com" {
				ip_info := loginRes.GetNetworkSectResp().GetBuiltinIplist().GetShortConnectIplist()
				for _, ip := range ip_info {
					host := ip.GetHost()
					host = strings.Replace(host, string(byte(0x00)), "", -1)
					if host == v.GetRedirect() {
						Wx_newshortext_Host.PushBack(host)
					}
				}
			}
		}
		return Data62(Data, Wx_newshort_Host.Front().Value.(string))
	}

	return models.ResponseResult{
		Code:    -8,
		Success: false,
		Message: "失败",
		Data:    loginRes,
	}
}
