package Login

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"net/url"
	"regexp"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

func PhoneDeviceLogin(Data PhoneDeviceLoginParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}
	req := &mm.GetA8KeyReq{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    D.Sessionkey,
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(0),
		},
		OpCode: proto.Uint32(2),
		Scope: &mm.SKBuiltinStringT{
			String_: proto.String(""),
		},
		ReqUrl: &mm.SKBuiltinStringT{
			String_: proto.String(Data.Url),
		},
		Scene:     proto.Uint32(4),
		UserName:  proto.String(Data.Wxid),
		BundleID:  proto.String(""),
		FontScale: proto.Uint32(118),
		NetType:   proto.String("WIFI"),
		RequestId: proto.Uint64(uint64(time.Now().Unix())),
		CodeType:  proto.Uint32(19),
		OuterUrl:  proto.String(""),
		SubScene:  proto.Uint32(1),
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/3rd-geta8key",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              226,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	resp := mm.GetA8KeyResp{}
	err = proto.Unmarshal(protobufdata, &resp)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}
	if resp.BaseResponse.GetRet() == 0 {
		urlv := resp.GetFullURL()
		v, _ := models.GetHTML(urlv, resp.HttpHeader)
		var action = regexp.MustCompile(`action\="([^"]*)"`)
		if len(action.FindStringSubmatch(v)) <= 0 {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("二维码已失效"),
				Data:    nil,
			}
		}
		postUrl := "https://login.weixin.qq.com" + action.FindStringSubmatch(v)[1]
		rsp, ck := models.GetHTML(postUrl, resp.HttpHeader)
		//fmt.Println(rsp)
		apiUrl := "https://login.weixin.qq.com" + action.FindStringSubmatch(rsp)[1]
		fmt.Println(apiUrl)
		data := url.Values{}
		time.Sleep(1 * time.Second)
		rspv := models.HttpPost(postUrl, data, ck)
		return models.ResponseResult{
			Code:    0,
			Success: true,
			Message: "成功",
			Data:    rspv,
		}
	}
	return models.ResponseResult{
		Code:    -8,
		Success: false,
		Message: fmt.Sprintf("请求失败：%v", resp.BaseResponse.ErrMsg),
		Data:    nil,
	}

}
