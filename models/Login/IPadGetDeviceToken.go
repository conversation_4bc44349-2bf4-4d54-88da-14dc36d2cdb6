package Login

import (
	"bytes"
	"compress/zlib"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/Mmtls"
	"wechatdll/comm"
	"wechatdll/lib"
	"wechatdll/models"

	"github.com/golang/protobuf/proto"
)

func IPadGetDeviceToken(DeviceId, DeviceType, DeviceName, OsVersion string, Version int32, httpclient Mmtls.HttpClientModel, proxy models.ProxyInfo) (mm.TrustResponse, error) {
	uuid1, uuid2 := Algorithm.IOSUuid(DeviceId)
	td := &mm.TrustReq{
		Td: &mm.TrustData{
			Tdi: []*mm.TrustDeviceInfo{
				{Key: proto.String("deviceid"), Val: proto.String(DeviceId)},
				{Key: proto.String("sdi"), Val: proto.String(Algorithm.IOSGetCidMd5(DeviceId, Algorithm.IOSGetCid(0x0262626262626)))},
				{Key: proto.String("idfv"), Val: proto.String(uuid1)},
				{Key: proto.String("idfa"), Val: proto.String(uuid2)},
				{Key: proto.String("device_model"), Val: proto.String(DeviceType)},
				{Key: proto.String("os_version"), Val: proto.String(OsVersion)},
				{Key: proto.String("core_count"), Val: proto.String("6")},
				{Key: proto.String("carrier_name"), Val: proto.String("")},
				{Key: proto.String("is_jailbreak"), Val: proto.String("0")},
				{Key: proto.String("device_name"), Val: proto.String(DeviceName)},
				{Key: proto.String("client_version"), Val: proto.String(fmt.Sprintf("%v", Version))},
				{Key: proto.String("plist_version"), Val: proto.String(fmt.Sprintf("%v", Version))},
				{Key: proto.String("language"), Val: proto.String("zh")},
				{Key: proto.String("locale_country"), Val: proto.String("CN")},
				{Key: proto.String("screen_width"), Val: proto.String("834")},
				{Key: proto.String("screen_height"), Val: proto.String("1112")},
				{Key: proto.String("install_time"), Val: proto.String("1586355322")},
				{Key: proto.String("kern_boottime"), Val: proto.String("1586355519000")},
			},
		},
	}

	pb, _ := proto.Marshal(td)

	var b bytes.Buffer
	w := zlib.NewWriter(&b)
	_, _ = w.Write(pb)
	_ = w.Close()

	// 使用07算法加密
	encData := Algorithm.SaeEncrypt07(b.Bytes())

	randKey := make([]byte, 16)
	_, _ = io.ReadFull(rand.Reader, randKey)

	fp := &mm.FPFresh{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    []byte{},
			Uin:           proto.Uint32(0),
			DeviceId:      append([]byte(DeviceId), 0),
			ClientVersion: proto.Int32(Version),
			DeviceType:    []byte(DeviceType),
			Scene:         proto.Uint32(0),
		},
		SessKey: randKey,
		Ztdata: &mm.ZTData{
			Version:   []byte("00000007"),
			Encrypted: proto.Uint32(1),
			Data:      encData,
			TimeStamp: proto.Uint32(uint32(time.Now().Unix())),
			Optype:    proto.Uint32(5),
			Uin:       proto.Uint32(0),
		},
	}

	reqdata, _ := proto.Marshal(fp)

	Host := comm.GetIp(comm.LoginData{})

	hec := &Algorithm.Client{}
	hec.Init("IOS")
	hecData := hec.HybridEcdhPackIosEn(3789, 0, nil, reqdata, []byte(""))
	recvData, err := httpclient.MMtlsPost(Host, "/cgi-bin/micromsg-bin/fpinitnl", hecData, proxy)

	if err != nil {
		return mm.TrustResponse{}, err
	}

	if len(recvData) <= 31 {
		return mm.TrustResponse{}, errors.New(hex.EncodeToString(recvData))
	}

	ph := hec.HybridEcdhPackIosUn(recvData)
	DTResp := &mm.TrustResponse{}
	_ = proto.Unmarshal(ph.Data, DTResp)
	return *DTResp, nil
}

func IPadUpdateDeviceToken(D comm.LoginData, DeviceType, OsVersion string, Version int32) (mm.TrustResponse, error) {
	DeviceId := D.Deviceid_str
	uuid1, uuid2 := Algorithm.IOSUuid(DeviceId)
	td := &mm.TrustReq{
		Td: &mm.TrustData{
			Tdi: []*mm.TrustDeviceInfo{
				{Key: proto.String("deviceid"), Val: proto.String(DeviceId)},
				{Key: proto.String("sdi"), Val: proto.String(Algorithm.IOSGetCidMd5(DeviceId, Algorithm.IOSGetCid(0x0262626262626)))},
				{Key: proto.String("idfv"), Val: proto.String(uuid1)},
				{Key: proto.String("idfa"), Val: proto.String(uuid2)},
				{Key: proto.String("device_model"), Val: proto.String(DeviceType)},
				{Key: proto.String("os_version"), Val: proto.String(OsVersion)},
				{Key: proto.String("core_count"), Val: proto.String("6")},
				{Key: proto.String("carrier_name"), Val: proto.String("")},
				{Key: proto.String("is_jailbreak"), Val: proto.String("0")},
				{Key: proto.String("device_name"), Val: proto.String(D.DeviceName)},
				{Key: proto.String("client_version"), Val: proto.String(fmt.Sprintf("%v", Version))},
				{Key: proto.String("plist_version"), Val: proto.String(fmt.Sprintf("%v", Version))},
				{Key: proto.String("language"), Val: proto.String("zh")},
				{Key: proto.String("locale_country"), Val: proto.String("CN")},
				{Key: proto.String("screen_width"), Val: proto.String("834")},
				{Key: proto.String("screen_height"), Val: proto.String("1112")},
				{Key: proto.String("install_time"), Val: proto.String("1586355322")},
				{Key: proto.String("kern_boottime"), Val: proto.String("1586355519000")},
			},
		},
	}

	pb, _ := proto.Marshal(td)

	var b bytes.Buffer
	w := zlib.NewWriter(&b)
	_, _ = w.Write(pb)
	_ = w.Close()

	// 使用07算法加密
	encData := Algorithm.SaeEncrypt07(b.Bytes())

	randKey := make([]byte, 16)
	_, _ = io.ReadFull(rand.Reader, randKey)

	fp := &mm.FPFresh{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    []byte{},
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(0),
		},
		Ztdata: &mm.ZTData{
			Version:   []byte("00000007"),
			Encrypted: proto.Uint32(1),
			Data:      encData,
			TimeStamp: proto.Uint32(uint32(time.Now().Unix())),
			Optype:    proto.Uint32(5),
			Uin:       proto.Uint32(0),
		},
	}

	reqdata, _ := proto.Marshal(fp)

	httpclient := Mmtls.GenNewHttpClient(D.MmtlsKey)

	Host := comm.GetIp(D)

	hec := &Algorithm.Client{}
	hec.Init("IOS")
	hecData := hec.HybridEcdhPackIosEn(836, D.Uin, D.Cooike, reqdata, D.Loginecdhkey)
	recvData, err := httpclient.MMtlsPost(Host, "/cgi-bin/micromsg-bin/fpfresh", hecData, D.Proxy)

	if err != nil {
		return mm.TrustResponse{}, err
	}

	if len(recvData) <= 31 {
		Ret, name := lib.RetConst(recvData)
		fmt.Println(Ret, name)
		return mm.TrustResponse{}, errors.New(hex.EncodeToString(recvData))
	}

	ph := hec.HybridEcdhPackIosUn(recvData)
	DTResp := &mm.TrustResponse{}
	_ = proto.Unmarshal(ph.Data, DTResp)
	return *DTResp, nil
}
