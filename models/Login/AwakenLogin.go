package Login

import (
	"encoding/hex"
	"fmt"
	"github.com/golang/protobuf/proto"
	"strconv"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/lib"
	"wechatdll/models"
)

func AwakenLogin(Data OsParam) models.ResponseResult {
	fmt.Println("这里是唤醒登录吧")

	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	//初始化Mmtls
	httpclient, MmtlsClient, err := comm.MmtlsInitialize(D.Proxy)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("MMTLS初始化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	var DeviceType string

	DeviceType = D.DeviceType

	if Data.OSModel == "windows" {
		DeviceType = "Windows 10"
	} else if Data.OSModel == "mac" {
		DeviceType = Algorithm.MacDeviceType
	} else if Data.OSModel == "QQBrowser" {
		DeviceType = "Windows-QQBrowser"
	}

	Autoauthkey := &mm.AutoAuthKey{}
	_ = proto.Unmarshal(D.Autoauthkey, Autoauthkey)

	clientID := strconv.Itoa(int(time.Now().UnixNano()))
	clientID = clientID[0:10] + "." + clientID[11:17]
	randKey := []byte(lib.RandSeq(16)) //获取随机密钥

	req := &mm.PushLoginURLRequest{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    D.Sessionkey,
			Uin:           proto.Uint32(0), //proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(DeviceType),
			Scene:         proto.Uint32(0),
		},
		//Autoauthticket: proto.String(""),
		Autoauthkey: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(D.Autoauthkeylen)),
			Buffer: D.Autoauthkey,
		},
		ClientId:   proto.String("iPad-Push-" + clientID),
		Devicename: proto.String(D.DeviceName),
		Opcode:     proto.Int32(3),
		RandomEncryKey: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(randKey))), ///proto.Uint32(uint32(len(D.Sessionkey))),
			Buffer: randKey,                            //D.Sessionkey,
		},
		Username: proto.String(D.Wxid),
		MsgContextPubKey: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Algorithm.RsaKeyPubKey))),
			Buffer: Algorithm.RsaKeyPubKey,
		},
	}

	Host := comm.GetIp(*D)

	reqdata1, _ := proto.Marshal(req.Autoauthkey)
	fmt.Println(hex.EncodeToString(reqdata1))
	reqdata2, _ := proto.Marshal(req.RandomEncryKey)
	fmt.Println(hex.EncodeToString(reqdata2))

	reqdata, err := proto.Marshal(req)
	fmt.Println(hex.EncodeToString(reqdata))

	hec := &Algorithm.Client{}
	hec.Init("IOS")
	hecData := hec.HybridEcdhPackIosEn(654, 0, D.Cooike, reqdata, D.Loginecdhkey)
	recvData, err := httpclient.MMtlsPost(Host, "/cgi-bin/micromsg-bin/pushloginurl", hecData, D.Proxy)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}
	fmt.Println(recvData)
	if len(recvData) <= 31 {
		Ret, name := lib.RetConst(recvData)
		fmt.Println("Ret", Ret)
		fmt.Println("name", name)
		error := "您已退出微信/session过期"
		if Ret != -13 {
			error = name
			if name == "" {
				error = "微信未知的错误信息"
			}
		}
		return models.ResponseResult{
			Code:    Ret,
			Success: false,
			Message: error,
			Data:    nil,
			Debug:   hex.EncodeToString(recvData),
		}
	}
	ph1 := hec.HybridEcdhPackIosUn(recvData)
	//解包
	Response := mm.PushLoginURLResponse{}
	err = proto.Unmarshal(ph1.Data, &Response)
	fmt.Println("err1", err)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	//保存redis
	err = comm.CreateLoginData(comm.LoginData{
		Uuid:                       Response.GetUuid(),
		Aeskey:                     D.Sessionkey,
		NotifyKey:                  Response.GetNotifyKey().GetBuffer(),
		Deviceid_str:               D.Deviceid_str,
		Deviceid_byte:              D.Deviceid_byte,
		DeviceName:                 D.DeviceName,
		DeviceType:                 DeviceType,
		ClientVersion:              Algorithm.IPadVersion,
		HybridEcdhPrivkey:          D.HybridEcdhPrivkey,
		HybridEcdhPubkey:           D.HybridEcdhPubkey,
		HybridEcdhInitServerPubKey: D.HybridEcdhInitServerPubKey,
		Cooike:                     ph1.Cookies,
		MmtlsKey:                   MmtlsClient,
	}, "", 300)
	fmt.Println("err2", err)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("Redis ERROR：%v", err.Error()),
			Data:    nil,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}
}
