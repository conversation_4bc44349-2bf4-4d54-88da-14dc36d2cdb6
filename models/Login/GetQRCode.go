package Login

import (
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"github.com/golang/protobuf/proto"
	"strings"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/lib"

	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

type GetQRReq struct {
	Proxy      models.ProxyInfo
	DeviceID   string
	DeviceName string
	OSModel    string
}

type GetQRRes struct {
	baseResponse GetQRResErr
	QrBase64     string
	Uuid         string
	QrUrl        string
	ExpiredTime  string
}

type GetQRResErr struct {
	Ret   int32
	Error string
}

func GetQRCODE(Data GetQRReq) models.ResponseResult2 {
	//初始化Mmtls
	httpclient, MmtlsClient, err := comm.MmtlsInitialize(Data.Proxy)
	if err != nil {
		return models.ResponseResult2{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("MMTLS初始化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	aeskey := []byte(lib.RandSeq(16)) //获取随机密钥

	deviceid := lib.CreateDeviceId(Data.DeviceID)
	devicelIdByte := []byte("")
	if strings.HasPrefix(deviceid, "A") {
		devicelIdByte = []byte(deviceid)
	} else {
		devicelIdByte, _ = hex.DecodeString(deviceid)
	}
	DeviceToken, err := IPadGetDeviceToken(deviceid, "iPad11,3", Data.DeviceName, "iOS14.2", int32(Algorithm.IPadVersion), *httpclient, Data.Proxy)

	if err != nil {
		DeviceToken = mm.TrustResponse{}
	}
	fmt.Println("请求数据:", DeviceToken)

	var DeviceType string

	DeviceType = Algorithm.IPadDeviceType
	version := Algorithm.WinVersion

	if Data.OSModel == "windows" {
		DeviceType = "Windows 10"
		version = Algorithm.WinVersion
	} else if Data.OSModel == "mac" {
		DeviceType = Algorithm.MacDeviceType
		version = Algorithm.MacVersion
	} else if Data.OSModel == "QQBrowser" {
		DeviceType = "Windows-QQBrowser"
	}
	req := &mm.GetLoginQRCodeRequest{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    []byte{},
			Uin:           proto.Uint32(0),
			DeviceId:      devicelIdByte,
			ClientVersion: proto.Int32(int32(version)),
			DeviceType:    []byte(""),
			Scene:         proto.Uint32(0),
		},
		RandomEncryKey: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(aeskey))),
			Buffer: aeskey,
		},
		Opcode: proto.Uint32(0),
		MsgContextPubKey: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Algorithm.RsaKeyPubKey))),
			Buffer: Algorithm.RsaKeyPubKey,
		},
	}

	reqdata, err := proto.Marshal(req)
	fmt.Println("CheckUuid:" + hex.EncodeToString(reqdata))
	fmt.Println("请求数据:", reqdata)

	if err != nil {
		return models.ResponseResult2{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	Host := comm.GetIp(comm.LoginData{})

	hec := &Algorithm.Client{}
	hec.Init("IOS")
	hypack := hec.HybridEcdhPackIosEn(502, 0, nil, reqdata, []byte(""))
	recvData, err := httpclient.MMtlsPost(Host, "/cgi-bin/micromsg-bin/getloginqrcode", hypack, Data.Proxy)

	fmt.Println("请求的地址：", Host)
	fmt.Println("请求的路径：", "/cgi-bin/micromsg-bin/getloginqrcode")
	fmt.Println("请求的数据明文：", reqdata)
	fmt.Println("请求的数据密文：", hypack)
	//recvData, err := httpclient.MMtlsPost(Host, "/cgi-bin/micromsg-bin/getloginqrcode/cgi-bin/micromsg-bin/uploadmcontact/cgi-bin/mmpay-bin/transferoperation", hypack, Data.Proxy)
	if err != nil {
		return models.ResponseResult2{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}
	if len(recvData) <= 31 {
		return models.ResponseResult2{
			Code:    -8,
			Success: false,
			Message: "微信返回的数据存在异常",
			Data:    hex.EncodeToString(recvData),
		}
	}
	ph1 := hec.HybridEcdhPackIosUn(recvData)
	getloginQRRes := mm.GetLoginQRCodeResponse{}

	err = proto.Unmarshal(ph1.Data, &getloginQRRes)

	if err != nil {
		return models.ResponseResult2{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	if getloginQRRes.GetBaseResponse().GetRet() == 0 {
		if getloginQRRes.Uuid == nil || *getloginQRRes.Uuid == "" {
			return models.ResponseResult2{
				Code:    -9,
				Success: false,
				Message: "取码过于频繁",
				Data:    getloginQRRes.GetBaseResponse(),
			}
		}

		//保存redis
		err := comm.CreateLoginData(comm.LoginData{
			Uuid:          getloginQRRes.GetUuid(),
			Aeskey:        aeskey,
			NotifyKey:     getloginQRRes.GetNotifyKey().GetBuffer(),
			Deviceid_str:  deviceid,
			Deviceid_byte: devicelIdByte,
			DeviceName:    Data.DeviceName,
			ClientVersion: version,
			DeviceType:    DeviceType,
			Cooike:        ph1.Cookies,
			Proxy:         Data.Proxy,
			MmtlsKey:      MmtlsClient,
			DeviceToken:   DeviceToken,
		}, "", 300)
		//Algorithm.GetiPadNewSpamData(deviceid, Data.DeviceName, DeviceToken, 0)
		if err == nil {
			return models.ResponseResult2{
				Code:    1,
				Success: true,
				Message: "成功",
				Data: GetQRRes{
					baseResponse: GetQRResErr{
						Ret:   getloginQRRes.GetBaseResponse().GetRet(),
						Error: getloginQRRes.GetBaseResponse().GetErrMsg().GetString_(),
					},
					Uuid:        getloginQRRes.GetUuid(),
					QrUrl:       "https://api.qrserver.com/v1/create-qr-code/?data=http://weixin.qq.com/x/" + getloginQRRes.GetUuid(),
					QrBase64:    fmt.Sprintf("data:image/jpg;base64,%v", base64.StdEncoding.EncodeToString(getloginQRRes.GetQrcode().GetBuffer())),
					ExpiredTime: time.Unix(int64(getloginQRRes.GetExpiredTime()), 0).Format("2006-01-02 15:04:05"),
				},
				Data62:   lib.Get62Data(deviceid),
				DeviceId: deviceid,
			}
		}
	}

	return models.ResponseResult2{
		Code:    -0,
		Success: false,
		Message: "未知的错误",
		Data:    getloginQRRes,
	}
}
