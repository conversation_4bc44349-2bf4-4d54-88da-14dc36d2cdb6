package Login

import (
	"wechatdll/Algorithm"
	"wechatdll/models"
)

type ExtDeviceLoginConfirmParam struct {
	Wxid string
	Url  string
}
type PhoneDeviceLoginParam struct {
	Wxid string
	Url  string
}

type Data62LoginReq struct {
	UserName   string
	Password   string
	Data62     string
	DeviceName string
	Proxy      models.ProxyInfo
}

type A16LoginParam struct {
	UserName   string
	Password   string
	A16        string
	DeviceName string
	Proxy      models.ProxyInfo
	Extend     Algorithm.AndroidDeviceInfo
}

type Rouses struct {
	Wxid  string
	Url   string
	OS    string
	Proxy models.ProxyInfo
}

type OsParam struct {
	Wxid    string
	OSModel string
}

type UpdateDeviceTokenParam struct {
	Wxid  string
	Model string
}
