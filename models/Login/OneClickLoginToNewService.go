package Login

import (
	"encoding/json"
	"fmt"
	"github.com/ddliu/go-httpclient"
	"github.com/tidwall/gjson"
	"strings"
	"wechatdll/models"
)

func OneClickLoginToNewService(Wxid, Url, OS string, Proxy models.ProxyInfo) models.ResponseResult {
	//先取码
	uuid, qrurl, err := getqr(Wxid, Url, OS, Proxy)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	if uuid != "" || qrurl != "" {
		qrurl := strings.Replace(qrurl, "http://qr.topscan.com/api.php?text=", "", -1)
		ext := extDeviceLoginConfirm(Wxid, qrurl)
		if ext {
			err := checkQR(uuid, Url)
			if err == nil {
				return models.ResponseResult{
					Code:    0,
					Success: true,
					Message: "成功",
					Data:    nil,
				}
			}
		}
	}

	return models.ResponseResult{
		Code:    -8,
		Success: false,
		Message: "推送登录失败,请重新尝试",
		Data:    nil,
	}

}

func checkQR(uuid, Url string) error {
	POSTData := map[string]interface{}{}
	POSTData["s"] = 0
	DataJson, _ := json.Marshal(POSTData)
	api := fmt.Sprintf("http://%v/813/Login/CheckQR?uuid=%v", Url, uuid)
	_, err := httpclient.PostJson(api, string(DataJson))
	if err != nil {
		return err
	}
	return nil
}

func extDeviceLoginConfirm(Wxid, Url string) bool {
	S := ExtDeviceLoginConfirmGet(ExtDeviceLoginConfirmParam{
		Wxid: Wxid,
		Url:  Url,
	})

	if S.Success != true {
		return false
	}

	SS := ExtDeviceLoginConfirmOk(ExtDeviceLoginConfirmParam{
		Wxid: Wxid,
		Url:  Url,
	})

	if SS.Success != true {
		return false
	}

	return true
}

func getqr(Wxid, Url, OS string, Proxy models.ProxyInfo) (string, string, error) {
	POSTData := GetQRReq{
		Proxy:      Proxy,
		DeviceID:   Wxid,
		DeviceName: "iPad",
		OSModel:    OS,
	}
	DataJson, _ := json.Marshal(POSTData)
	fmt.Println("=========================================")
	fmt.Println(Url)

	api := "http://" + Url + "/813/Login/GetQR"
	ret, err := httpclient.PostJson(api, string(DataJson))
	if err != nil {
		return "", "", err
	}

	defer ret.Body.Close()
	bodyString, err := ret.ToString()
	if err != nil {
		return "", "", err
	}

	Uuid := gjson.Get(bodyString, "Data.Uuid").String()
	QrUrl := gjson.Get(bodyString, "Data.QrUrl").String()

	return Uuid, QrUrl, nil
}
