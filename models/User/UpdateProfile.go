package User

import (
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"
)

func UpdateProfile(Data UpdateProfileParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}

	info := GetContractProfile(D.Wxid)

	MM, _ := json.Marshal(info.Data)

	var ModUserInfo mm.GetProfileResponse

	_ = json.Unmarshal(MM, &ModUserInfo)

	var CmdId int32
	var userInfoSerialize []byte

	//修改昵称
	if Data.NickName != "" && Data.NickName != "string" {
		userInfo := &mm.UPUserInfo{}
		CmdId = 64
		userInfo.BitFlag = proto.Uint32(1)
		userInfo.NickName = proto.String(Data.NickName)
		userInfoSerialize, _ = proto.Marshal(userInfo)
	} else {
		CmdId = 1
		var Signature, Province, City, Country string

		if Data.Signature != "" && Data.Signature != "string" {
			Signature = Data.Signature
		} else {
			Signature = ModUserInfo.GetUserInfo().GetSignature()
		}

		//国家
		if Data.Country != "" && Data.Country != "string" {
			Country = Data.Country
		} else {
			Country = ModUserInfo.GetUserInfo().GetCountry()
		}

		//省
		if Data.Province != "" && Data.Province != "string" {
			Province = Data.Province
		} else {
			Province = ModUserInfo.GetUserInfo().GetProvince()
		}

		//市
		if Data.City != "" && Data.City != "string" {
			City = Data.City
		} else {
			City = ModUserInfo.GetUserInfo().GetCity()
		}

		userInfo := &mm.ModUserInfo{
			BitFlag: proto.Uint32(2178),
			UserName: &mm.SKBuiltinStringT{
				String_: proto.String(Data.Wxid),
			},
			NickName: &mm.SKBuiltinStringT{
				String_: proto.String(ModUserInfo.GetUserInfo().GetNickName().GetString_()),
			},
			BindUin:    proto.Uint32(0),
			BindEmail:  &mm.SKBuiltinStringT{},
			BindMobile: &mm.SKBuiltinStringT{},
			Status:     proto.Uint32(ModUserInfo.GetUserInfo().GetStatus()),
			ImgLen:     proto.Uint32(0),
			Sex:        proto.Int(int(Data.Sex)),
			Province:   proto.String(Province),
			City:       proto.String(City),
			Signature:  proto.String(Signature),
			PluginFlag: proto.Uint32(ModUserInfo.GetUserInfo().GetPluginFlag()),
			Country:    proto.String(Country),
		}
		userInfoSerialize, _ = proto.Marshal(userInfo)
	}

	var CmdItem []*mm.CmdItem

	CmdItem = append(CmdItem, &mm.CmdItem{
		CmdId: proto.Int32(CmdId),
		CmdBuf: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(userInfoSerialize))),
			Buffer: userInfoSerialize,
		},
	})

	req := &mm.OpLogRequest{
		Cmd: &mm.CmdList{
			Count: proto.Uint32(uint32(len(CmdItem))),
			List:  CmdItem,
		},
	}

	//序列化
	reqdata, _ := proto.Marshal(req)

	Host := comm.GetIp(*D)

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Host:   Host,
		Cgiurl: "/cgi-bin/micromsg-bin/oplog",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              681,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.Loginecdhkey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := mm.OplogResponse{}
	err = proto.Unmarshal(protobufdata, &Response)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}

	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    Response,
	}
}
