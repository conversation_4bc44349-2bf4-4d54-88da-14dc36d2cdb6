package User

import (
	"wechatdll/bts"
	"wechatdll/models"
)

func OnekeySetPasswd(Data OnekeySetPasswdParam) models.ResponseResult {
	//先验证密码
	VerifyPwd := NewVerifyPasswd(NewVerifyPasswdParam{
		Wxid:     Data.Wxid,
		Password: Data.OldPassword,
	})

	if VerifyPwd.Success == false {
		return models.ResponseResult{
			Code:    VerifyPwd.Code,
			Success: VerifyPwd.Success,
			Message: VerifyPwd.Message,
			Data:    VerifyPwd.Data,
		}
	}

	Ticket := bts.GetVerifyPswdResponse(VerifyPwd.Data)

	if Ticket.Ticket == nil {
		return models.ResponseResult{
			Code:    VerifyPwd.Code,
			Success: VerifyPwd.Success,
			Message: VerifyPwd.Message,
			Data:    Ticket,
		}
	}

	//修改密码
	Revise := NewSetPasswd(NewSetPasswdParam{
		Wxid:        Data.Wxid,
		NewPassword: Data.NewPassword,
		Ticket:      Ticket.GetTicket(),
	})

	return models.ResponseResult{
		Code:    Revise.Code,
		Success: Revise.Success,
		Message: Revise.Message,
		Data:    Revise.Data,
	}

}
