package Callback

func (T *Task) MsgSyncAdd(Data MsgSyncPushTask) {
	T.runningMu.Lock()
	defer T.runningMu.Unlock()
	entry := T.check(Data.Wxid)
	//判断任务是否为空
	if entry.Wxid == "" {
		T.serves = append(T.serves, &Serve{
			Wxid:      "",
			MsgSync:   false,
			Heartbeat: false,
		})
	}

	if entry.MsgSync.ID == 0 || entry.MsgSync.State == "STOPPED" {
		go runMsgSync(Data.Wxid)
	}
}

func runMsgSync(wxid string) {

}
