# 好友关系检测日志功能修改总结

## 问题描述
好友关系检测功能之前能正常返回1450，现在只返回0，需要添加详细日志来诊断问题。

## 修改内容

### 1. 修改的文件
- `models/Friend/FriendRelation.go` - 主要修改文件

### 2. 添加的功能
- **日志文件创建函数** (`createLogFile`)：在根目录创建 `friend_relation_debug.log` 文件
- **日志写入函数** (`writeLog`)：同时写入文件和控制台输出
- **详细的日志记录**：覆盖整个好友关系检测流程

### 3. 日志记录的关键节点
1. **请求开始**：记录输入参数（Wxid, UserName）
2. **登录数据获取**：记录是否成功及关键信息（Uin, SessionKey长度等）
3. **请求构建**：记录请求参数和序列化结果
4. **网络请求**：记录Host、URL、请求结果和响应数据长度
5. **响应解析**：记录反序列化结果和详细响应内容
6. **最终结果**：记录好友关系状态码及含义

### 4. 新增的导入包
```go
import (
    "log"      // 控制台日志输出
    "os"       // 文件操作
    "time"     // 时间戳
)
```

## 创建的辅助文件

### 1. 日志文件
- `friend_relation_debug.log` - 日志输出文件（项目根目录）

### 2. 说明文档
- `好友关系检测日志说明.md` - 详细的使用说明和问题诊断指南

### 3. 测试脚本
- `test_api.sh` - API测试脚本，可用于快速测试功能

## 使用方法

### 1. 启动服务
```bash
./wechatdll
# 或
go run main.go
```

### 2. 调用API
```bash
curl -X POST "http://localhost:8085/api/Friend/GetFriendRelation" \
     -H "Content-Type: application/json" \
     -d '{
       "Wxid": "your_wxid",
       "UserName": "target_username"
     }'
```

### 3. 查看日志
```bash
tail -f friend_relation_debug.log
```

## 日志示例
```
[2025-01-08 15:30:00] 开始好友关系检测 - Wxid: test_wxid, UserName: test_username
[2025-01-08 15:30:00] 成功获取登录数据 - Uin: 123456, SessionKey长度: 32, DeviceId长度: 16
[2025-01-08 15:30:00] 开始构建请求数据
[2025-01-08 15:30:00] 请求参数 - AppId: wx7c8d593b2c3a7703, UserName: test_username, Uin: 123456, ClientVersion: 807
[2025-01-08 15:30:00] 请求序列化成功，数据长度: 128
[2025-01-08 15:30:00] 开始发送网络请求 - Host: xxx.xxx.xxx.xxx, CGI: 1177
[2025-01-08 15:30:01] 网络请求成功，响应数据长度: 256
[2025-01-08 15:30:01] 开始解析响应数据
[2025-01-08 15:30:01] 响应反序列化成功
[2025-01-08 15:30:01] BaseResponse - Ret: 0, ErrMsg: 
[2025-01-08 15:30:01] 响应详情 - Openid: xxx, NickName: xxx, HeadImgUrl: xxx, Sign: xxx, FriendRelation: 0
[2025-01-08 15:30:01] 好友关系检测完成 - 关系状态: 0 (1=删除, 4=自己拉黑, 5=被拉黑, 0=正常)
```

## 问题诊断指南

### 常见问题及解决方案
1. **获取登录数据失败** - 检查Wxid是否正确，是否已登录
2. **网络请求失败** - 检查网络连接和服务器状态
3. **响应解析失败** - 检查服务端返回数据格式
4. **FriendRelation为0但期望1450** - 检查BaseResponse的Ret和ErrMsg，可能是微信服务端问题

## 注意事项
- 日志文件会持续追加，建议定期清理
- 包含敏感信息，请妥善保管日志文件
- 日志同时输出到控制台和文件，便于实时监控
- 修改不影响原有功能，只是增加了日志记录
