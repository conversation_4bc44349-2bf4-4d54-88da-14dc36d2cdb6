package bts

import (
	"encoding/json"
	"wechatdll/Cilent/mm"
	"wechatdll/models/Login"
	"wechatdll/models/Msg"
)

func SearchContactResponse(Data interface{}) mm.SearchContactResponse {
	var Buff mm.SearchContactResponse
	result, err := json.Marshal(&Data)
	if err != nil {
		return mm.SearchContactResponse{}
	}
	_ = json.Unmarshal(result, &Buff)

	return Buff
}

func GetContactResponse(Data interface{}) mm.GetContactResponse {
	var Buff mm.GetContactResponse
	result, err := json.Marshal(&Data)
	if err != nil {
		return mm.GetContactResponse{}
	}
	_ = json.Unmarshal(result, &Buff)
	return Buff
}

func GetModContact(Data interface{}) mm.ModContact {
	var Buff mm.ModContact
	result, err := json.Marshal(&Data)
	if err != nil {
		return mm.ModContact{}
	}
	_ = json.Unmarshal(result, &Buff)
	return Buff
}

func GetA8KeyResponse(Data interface{}) mm.GetA8KeyResp {
	var Buff mm.GetA8KeyResp
	result, err := json.Marshal(&Data)
	if err != nil {
		return mm.GetA8KeyResp{}
	}
	_ = json.Unmarshal(result, &Buff)
	return Buff
}

func GetVerifyPswdResponse(Data interface{}) mm.VerifyPswdResponse {
	var Buff mm.VerifyPswdResponse
	result, err := json.Marshal(&Data)
	if err != nil {
		return mm.VerifyPswdResponse{}
	}
	_ = json.Unmarshal(result, &Buff)
	return Buff
}

func GetSyncResponse(Data interface{}) Msg.SyncResponse {
	var Buff Msg.SyncResponse
	result, err := json.Marshal(&Data)
	if err != nil {
		return Msg.SyncResponse{}
	}
	_ = json.Unmarshal(result, &Buff)
	return Buff
}

func GetNewinitResponse(Data interface{}) Login.NewinitResponse {
	var Buff Login.NewinitResponse
	result, err := json.Marshal(&Data)
	if err != nil {
		return Login.NewinitResponse{}
	}
	_ = json.Unmarshal(result, &Buff)
	return Buff
}

func GetProfile(Data interface{}) mm.GetProfileResponse {
	var Buff mm.GetProfileResponse
	result, err := json.Marshal(&Data)
	if err != nil {
		return mm.GetProfileResponse{}
	}
	_ = json.Unmarshal(result, &Buff)
	return Buff
}
