package Algorithm

import (
	"bytes"
	"crypto/md5"
	"encoding/binary"
	"github.com/golang/protobuf/proto"
	"hash/adler32"

	//"hash/crc32"
	"io"
	"wechatdll/lib"
)

func (h *Client) HybridEcdhPackIosEn(Cgi, Uin uint32, Cookies, Data, loginecdhkey []byte) []byte {
	header := new(bytes.Buffer)
	header.Write([]byte{0xbf})
	header.Write([]byte{0x02}) //加密模式占坑,默认不压缩走12

	encryptdata := h.encryptoIOS(Data)

	cookielen := len(Cookies)
	header.Write([]byte{byte((12 << 4) + cookielen)})
	binary.Write(header, binary.BigEndian, int32(h.Version))
	if Uin != 0 {
		binary.Write(header, binary.BigEndian, int32(Uin))
	} else {
		header.Write([]byte{0x00, 0x00, 0x00, 0x00})
	}

	if len(Cookies) == 0xF {
		header.Write(Cookies)
	}

	header.Write(proto.EncodeVarint(uint64(Cgi)))
	header.Write(proto.EncodeVarint(uint64(len(encryptdata))))
	header.Write(proto.EncodeVarint(uint64(len(encryptdata))))
	header.Write(proto.EncodeVarint(10003))
	header.Write([]byte{0x00})
	header.Write(proto.EncodeVarint(uint64(GenSignature(Uin, loginecdhkey, Data))))
	header.Write([]byte{0xff})
	header.Write(proto.EncodeVarint(uint64(CalcMsgCrcForData_807(encryptdata))))
	header.Write([]byte{0x00})
	lens := len(header.Bytes())<<2 + 2
	header.Bytes()[1] = byte(lens)
	header.Write(encryptdata)
	return header.Bytes()
}

func (h *Client) HybridEcdhPackIosUn(Data []byte) *PacketHeader {
	var ph PacketHeader
	var body []byte
	var nCur int64
	var bfbit byte
	srcreader := bytes.NewReader(Data)
	binary.Read(srcreader, binary.BigEndian, &bfbit)
	if bfbit == byte(0xbf) {
		nCur += 1
	}
	nLenHeader := Data[nCur] >> 2
	nCur += 1
	nLenCookie := Data[nCur] & 0xf
	nCur += 1
	nCur += 4
	srcreader.Seek(nCur, io.SeekStart)
	binary.Read(srcreader, binary.BigEndian, &ph.Uin)
	nCur += 4
	cookie_temp := Data[nCur : nCur+int64(nLenCookie)]
	ph.Cookies = cookie_temp
	nCur += int64(nLenCookie)
	cgidata := Data[nCur:]
	_, nSize := proto.DecodeVarint(cgidata)
	nCur += int64(nSize)
	LenProtobufData := Data[nCur:]
	_, nLenProtobuf := proto.DecodeVarint(LenProtobufData)
	nCur += int64(nLenProtobuf)
	body = Data[nLenHeader:]
	protobufdata := h.decryptoIOS(body)
	ph.Data = protobufdata
	return &ph
}

func (h *Client) HybridEcdhPackAndroidEn(cmdid, cert, uin uint32, cookie, Data []byte) []byte {
	EnData := h.encryptAndroid(Data)
	inputlen := len(EnData)
	pack := append([]byte{}, cookie...)
	pack = proto.EncodeVarint(uint64(cmdid))
	pack = append(pack, proto.EncodeVarint(uint64(inputlen))...)
	pack = append(pack, proto.EncodeVarint(uint64(inputlen))...)
	pack = append(pack, proto.EncodeVarint(uint64(cert))...)
	pack = append(pack, 2)
	pack = append(pack, 0)
	pack = append(pack, 0xfe)
	pack = append(pack, proto.EncodeVarint(uint64(CalcMsgCrcForData_807(EnData)))...)
	pack = append(pack, 0)
	headLen := len(pack) + 11
	headFlag := (12 << 12) | (len(cookie) << 8) | (headLen << 2) | 2
	var hybridpack = new(bytes.Buffer)
	hybridpack.WriteByte(0xbf)
	binary.Write(hybridpack, binary.LittleEndian, uint16(headFlag))
	binary.Write(hybridpack, binary.BigEndian, uint32(h.Version))
	binary.Write(hybridpack, binary.BigEndian, uint32(uin))
	hybridpack.Write(pack)
	hybridpack.Write(EnData)
	return hybridpack.Bytes()
}

func (h *Client) HybridEcdhPackAndroidUn(Data []byte) *PacketHeader {
	var ph PacketHeader
	readHeader := bytes.NewReader(Data)
	binary.Read(readHeader, binary.LittleEndian, &ph.PacketCryptType)
	binary.Read(readHeader, binary.LittleEndian, &ph.Flag)
	cookieLen := (ph.Flag >> 8) & 0x0f
	headerLen := (ph.Flag & 0xff) >> 2
	ph.Cookies = make([]byte, cookieLen)
	binary.Read(readHeader, binary.BigEndian, &ph.RetCode)
	binary.Read(readHeader, binary.BigEndian, &ph.UICrypt)
	binary.Read(readHeader, binary.LittleEndian, &ph.Cookies)
	ph.Data = h.decryptAndroid(Data[headerLen:])
	return &ph
}

func Pack(src []byte, cgi int, uin uint32, sessionkey, cookies, clientsessionkey, loginecdhkey []byte, encryptType uint8, use_compress bool) []byte {
	len_proto_compressed := len(src)
	var body []byte
	if use_compress {
		if cgi == 138 {
			encryptType = 13
			mNonce := []byte(lib.RandSeq(12)) //获取随机密钥
			body = AesGcmEncryptWithCompressZlib(clientsessionkey, src, mNonce, nil)
		} else {
			body = CompressAndAes(src, sessionkey)
		}
	} else {
		if cgi == 138 {
			encryptType = 13
			mNonce := []byte(lib.RandSeq(12)) //获取随机密钥
			body = AesGcmEncryptWithCompressZlib(clientsessionkey, src, mNonce, nil)
		} else {
			body = AesEncrypt(src, sessionkey)
		}
	}

	//loginecdhkeylen := int32(len(loginecdhkey))

	header := new(bytes.Buffer)
	header.Write([]byte{0xbf})
	header.Write([]byte{0x00})
	header.Write([]byte{((encryptType << 4) + 0xf)})
	binary.Write(header, binary.BigEndian, int32(IPadVersion))
	binary.Write(header, binary.BigEndian, int32(uin))
	header.Write(cookies)
	header.Write(proto.EncodeVarint(uint64(cgi)))

	if use_compress {
		header.Write(proto.EncodeVarint(uint64(len_proto_compressed)))
		header.Write(proto.EncodeVarint(uint64(len(body))))
	} else {
		header.Write(proto.EncodeVarint(uint64(len_proto_compressed)))
		header.Write(proto.EncodeVarint(uint64(len_proto_compressed)))
	}

	header.Write([]byte{0x00, 0x0d}) //占坑
	//签名算法开始
	header.Write(proto.EncodeVarint(uint64(GenSignature(uin, loginecdhkey, src))))
	//签名算法结束
	header.Write([]byte{0xFF})                                            //占坑
	header.Write(proto.EncodeVarint(uint64(CalcMsgCrcForData_807(body)))) //占坑
	header.Write([]byte{0x00})                                            //占坑
	if use_compress {
		lens := (len(header.Bytes()) << 2) + 1
		header.Bytes()[1] = byte(lens)
	} else {
		lens := (len(header.Bytes()) << 2) + 2
		header.Bytes()[1] = byte(lens)
	}
	header.Write(body)
	return header.Bytes()
}

func UnpackBusinessPacket(src []byte, key []byte, uin uint32, cookie *[]byte) []byte {
	var nCur int64
	var bfbit byte
	srcreader := bytes.NewReader(src)
	binary.Read(srcreader, binary.BigEndian, &bfbit)
	if bfbit == byte(0xbf) {
		nCur += 1
	}
	nLenHeader := src[nCur] >> 2
	bUseCompressed := src[nCur] & 0x3
	nCur += 1
	nLenCookie := src[nCur] & 0xf
	nCur += 1
	nCur += 4
	srcreader.Seek(nCur, io.SeekStart)
	binary.Read(srcreader, binary.BigEndian, &uin)
	nCur += 4
	cookie_temp := src[nCur : nCur+int64(nLenCookie)]
	*cookie = cookie_temp
	nCur += int64(nLenCookie)
	cgidata := src[nCur:]
	_, nSize := proto.DecodeVarint(cgidata)
	nCur += int64(nSize)
	LenProtobufData := src[nCur:]
	_, nLenProtobuf := proto.DecodeVarint(LenProtobufData)
	nCur += int64(nLenProtobuf)
	body := src[nLenHeader:]
	if bUseCompressed == 1 {
		protobufData := DecompressAndAesDecrypt(body, key)
		return protobufData
	} else {
		protobufData := AesDecrypt(body, key)
		return protobufData
	}
}

func UnpackBusinessPacketWithAesGcm(src []byte, uin uint32, cookie *[]byte, Serversessionkey []byte) []byte {
	var nCur int64
	var bfbit byte
	srcreader := bytes.NewReader(src)
	binary.Read(srcreader, binary.BigEndian, &bfbit)
	if bfbit == byte(0xbf) {
		nCur += 1
	}
	nLenHeader := src[nCur] >> 2
	nCur += 1
	nLenCookie := src[nCur] & 0xf
	nCur += 1
	nCur += 4
	srcreader.Seek(nCur, io.SeekStart)
	binary.Read(srcreader, binary.BigEndian, &uin)
	nCur += 4
	cookie_temp := src[nCur : nCur+int64(nLenCookie)]
	*cookie = cookie_temp
	nCur += int64(nLenCookie)
	cgidata := src[nCur:]
	_, nSize := proto.DecodeVarint(cgidata)
	nCur += int64(nSize)
	LenProtobufData := src[nCur:]
	_, nLenProtobuf := proto.DecodeVarint(LenProtobufData)
	nCur += int64(nLenProtobuf)
	body := src[nLenHeader:]
	protobufdata := AesGcmDecryptWithcompressZlib(Serversessionkey, body, nil)
	return protobufdata
}

func GenSignature(uiCryptin uint32, salt, data []byte) uint32 {
	var b1 bytes.Buffer
	binary.Write(&b1, binary.BigEndian, uiCryptin)
	h1 := md5.New()
	h1.Write(b1.Bytes())
	h1.Write(salt)
	sum1 := h1.Sum(nil)

	dataSize := len(data)
	var b2 bytes.Buffer
	binary.Write(&b2, binary.BigEndian, uint32(dataSize))

	h2 := md5.New()
	h2.Write(b2.Bytes())
	h2.Write(salt)
	h2.Write(sum1)
	sum2 := h2.Sum(nil)

	a := adler32.New()
	a.Write(nil)
	a.Write(sum2)
	a.Write(data)
	return a.Sum32()
}
