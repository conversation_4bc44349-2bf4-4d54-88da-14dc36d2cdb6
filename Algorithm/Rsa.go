package Algorithm

import (
	"bytes"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/hex"
	"encoding/pem"
	"errors"
	"math/big"
)

var encAesKeyPublicKey = []byte(`-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDoqn5y8C1D0g8LVr/LSmCBiNJC
4TdPsHa+qWBuq/xEhv01MVzIoN5Vc8UZ0qLjDeWh462Ou2Ps6E6O1C+VSTBxtLrp
zBOxB/iAUKgw5we4G8kNijeVumVro/AovUeqUJRrMVkSBMbp/O1WZfiK7bQN0UxF
zpQe0j8HZ9hwGWnjSQIDAQAB
-----END PUBLIC KEY-----`)

var RsaKeyPubKey = []byte(`-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDHTEoNdw+Aa1Bh2T2sON5C5aYA
NdOKtir5gbUJidblsD3MzYgiEe6Xj4jecmDPHDol5ujdZC4EnF1vckePg76DXrAz
0EOxovJidtioD6yRD8jdrlCgMbG/s49VHXG2sfI7y9wrxQyhO3UDGKZubDd79E7n
LvTKvH+67ITDXSFcowIDAQAB
-----END PUBLIC KEY-----
`)

func Compress_rsa(data []byte, Key string) []byte {
	strOut := new(bytes.Buffer)
	var publicKey rsa.PublicKey
	s, _ := hex.DecodeString(Key)
	publicKey.N = new(big.Int).SetBytes(s)
	rsaLen := len(Key) / 8
	if len(data) > (rsaLen - 12) {
		blockCnt := 1
		if ((len(data) / (rsaLen - 12)) + (len(data) % (rsaLen - 12))) == 0 {
			blockCnt = 0
		}

		for i := 0; i < blockCnt; i++ {
			blockSize := rsaLen - 12
			if i == blockCnt-1 {
				blockSize = len(data) - i*blockSize
			}
			temp := data[(i * (rsaLen - 12)):(i*(rsaLen-12) + blockSize)]
			encrypted, _ := rsa.EncryptPKCS1v15(rand.Reader, &publicKey, temp)
			strOut.Write(encrypted)
		}
		return strOut.Bytes()
	}

	encrypted, err := rsa.EncryptPKCS1v15(rand.Reader, &publicKey, data)
	if err != nil {
		return []byte{}
	}
	return encrypted
}

//RSAEncrypt Rsa加密
func RSAEncrypt(data []byte, Key string) []byte {
	m := Key
	M := new(big.Int)
	M.SetString(m, 16)
	pub := rsa.PublicKey{}
	pub.E = 65537
	pub.N = M
	out, _ := rsa.EncryptPKCS1v15(rand.Reader, &pub, data)
	return out
}

// EncKeyRsaEncrypt 加密
func EncKeyRsaEncrypt(origData []byte) ([]byte, error) {
	block, _ := pem.Decode(encAesKeyPublicKey)
	if block == nil {
		return nil, errors.New("public key error")
	}
	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	pub := pubInterface.(*rsa.PublicKey)
	return rsa.EncryptPKCS1v15(rand.Reader, pub, origData)
}
