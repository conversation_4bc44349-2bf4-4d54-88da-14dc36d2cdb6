package Algorithm

import (
	"bytes"
	"compress/zlib"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"hash/crc32"
	"math/rand"
	"strings"
	"time"
	"wechatdll/Cilent/mm"

	//"github.com/go-gl/mathgl/mgl32"
	"github.com/golang/protobuf/proto"
)

// 注释掉未使用的CCD相关代码
/*
// CCD 结构体定义，用于实现07算法的逆运动学计算
type CCD struct {
	joints []Joint // 关节列表
}

// Joint 接口定义，每个关节需要实现的方法
type Joint interface {
	GetPosition() mgl32.Vec3            // 获取关节位置
	UpdateRotation(rotation mgl32.Quat) // 更新关节旋转
}

// NewCCD 创建新的CCD实例
func NewCCD(joints []Joint) *CCD {
	return &CCD{
		joints: joints,
	}
}

// GetEndEffectorPosition 获取末端执行器位置
func (c *CCD) GetEndEffectorPosition() mgl32.Vec3 {
	if len(c.joints) == 0 {
		return mgl32.Vec3{}
	}
	return c.joints[len(c.joints)-1].GetPosition()
}

// AddJoint 添加关节到CCD系统
func (c *CCD) AddJoint(joint Joint) {
	c.joints = append(c.joints, joint)
}
*/

func Wcstf(Username string, T int64) []byte {
	curtime := uint64(T / 1e6)
	contentlen := len(Username)

	var ct []uint64
	ut := curtime
	for i := 0; i < contentlen; i++ {
		ut += uint64(rand.Intn(10000))
		ct = append(ct, ut)
	}
	ccd := &mm.Wcstf{
		StartTime: &curtime,
		CheckTime: &curtime,
		Count:     proto.Uint32(uint32(contentlen)),
		EndTime:   ct,
	}

	pb, _ := proto.Marshal(ccd)

	var b bytes.Buffer
	w := zlib.NewWriter(&b)
	_, _ = w.Write(pb)
	_ = w.Close()

	zt := new(ZT)
	zt.Init(true)
	encData := zt.Encrypt(b.Bytes())

	Ztdata := &mm.ZTData{
		Version:   []byte("00000007"),
		Encrypted: proto.Uint32(1),
		Data:      encData,
		TimeStamp: proto.Uint32(uint32(T)),
		Optype:    proto.Uint32(5),
		Uin:       proto.Uint32(0),
	}
	MS, _ := proto.Marshal(Ztdata)
	return MS
}

func Wcste(A, B uint64, T int64) []byte {

	curtime := uint32(T)
	curNanoTime := uint64(time.Now().UnixNano())

	ccd := &mm.Wcste{
		Checkid:   proto.String("<LoginByID>"),
		StartTime: &curtime,
		CheckTime: &curtime,
		Count1:    proto.Uint32(0),
		Count2:    proto.Uint32(1),
		Count3:    proto.Uint32(0),
		Const1:    proto.Uint64(A),
		Const2:    &curNanoTime,
		Const3:    &curNanoTime,
		Const4:    &curNanoTime,
		Const5:    &curNanoTime,
		Const6:    proto.Uint64(B),
	}

	pb, _ := proto.Marshal(ccd)

	var b bytes.Buffer
	w := zlib.NewWriter(&b)
	_, _ = w.Write(pb)
	_ = w.Close()

	zt := new(ZT)
	zt.Init(true)
	encData := zt.Encrypt(b.Bytes())

	Ztdata := &mm.ZTData{
		Version:   []byte("00000007"),
		Encrypted: proto.Uint32(1),
		Data:      encData,
		TimeStamp: proto.Uint32(uint32(T)),
		Optype:    proto.Uint32(5),
		Uin:       proto.Uint32(0),
	}

	MS, _ := proto.Marshal(Ztdata)
	return MS
}

func AndroidCcData(DeviceId string, info AndroidDeviceInfo, DeviceToken mm.TrustResponse, T int64) *mm.ZTData {
	curtime := uint32(T)
	ccd3body := &mm.AndroidSpamDataBody{
		Loc:                  proto.Uint32(0),
		Root:                 proto.Uint32(0),
		Debug:                proto.Uint32(0),
		PackageSign:          proto.String(info.AndriodPackageSign(DeviceId)),
		RadioVersion:         proto.String(info.AndroidRadioVersion(DeviceId)),
		BuildVersion:         proto.String(info.AndroidVersion()),
		DeviceId:             proto.String(info.AndriodImei(DeviceId)),
		AndroidId:            proto.String(info.AndroidBuildID(DeviceId)),
		SerialId:             proto.String(info.AndriodPhoneSerial(DeviceId)),
		Model:                proto.String(info.AndroidPhoneModel(DeviceId)),
		CpuCount:             proto.Uint32(8),
		CpuBrand:             proto.String(info.AndroidHardware(DeviceId)),
		CpuExt:               proto.String(info.AndroidFeatures()),
		WlanAddress:          proto.String(info.AndriodWLanAddress(DeviceId)),
		Ssid:                 proto.String(info.AndriodSsid(DeviceId)),
		Bssid:                proto.String(info.AndriodBssid(DeviceId)),
		SimOperator:          proto.String(""),
		WifiName:             proto.String(info.AndroidWifiName(DeviceId)),
		BuildFP:              proto.String(info.AndroidBuildFP(DeviceId)),
		BuildBoard:           proto.String("bullhead"),
		BuildBootLoader:      proto.String(info.AndroidBuildBoard(DeviceId)),
		BuildBrand:           proto.String("google"),
		BuildDevice:          proto.String("bullhead"),
		GsmSimOperatorNumber: proto.String(""),
		SoterId:              proto.String(""),
		KernelReleaseNumber:  proto.String(info.AndroidKernelReleaseNumber(DeviceId)),
		UsbState:             proto.Uint32(0),
		Sign:                 proto.String(info.AndriodPackageSign(DeviceId)),
		PackageFlag:          proto.Uint32(14),
		AccessFlag:           proto.Uint32(uint32(info.AndriodAccessFlag(DeviceId))),
		Unkonwn:              proto.Uint32(3),
		TbVersionCrc:         proto.Uint32(uint32(info.AndriodTbVersionCrc(DeviceId))),
		SfMD5:                proto.String(info.AndriodSfMD5(DeviceId)),
		SfArmMD5:             proto.String(info.AndriodSfArmMD5(DeviceId)),
		SfArm64MD5:           proto.String(info.AndriodSfArm64MD5(DeviceId)),
		SbMD5:                proto.String(info.AndriodSbMD5(DeviceId)),
		SoterId2:             proto.String(""),
		WidevineDeviceID:     proto.String(info.AndriodWidevineDeviceID(DeviceId)),
		FSID:                 proto.String(info.AndriodFSID(DeviceId)),
		Oaid:                 proto.String(""),
		TimeCheck:            proto.Uint32(0),
		NanoTime:             proto.Uint32(uint32(info.AndriodNanoTime(DeviceId))),
		Refreshtime:          proto.Uint32(DeviceToken.GetTrustResponseData().GetTimeStamp()),
		SoftConfig:           proto.String(DeviceToken.GetTrustResponseData().GetSoftData().GetSoftConfig()),
		SoftData:             DeviceToken.GetTrustResponseData().GetSoftData().GetSoftData(),
	}

	pb, _ := proto.Marshal(ccd3body)

	crc := crc32.ChecksumIEEE(pb)

	ccd3 := &mm.AndroidCcdDataBody{
		Crc:       &crc,
		TimeStamp: &curtime,
		Body:      ccd3body,
	}

	pb, _ = proto.Marshal(ccd3)

	var b bytes.Buffer
	w := zlib.NewWriter(&b)
	_, _ = w.Write(pb)
	_ = w.Close()

	zt := new(ZT)
	zt.Init(false)
	encData := zt.Encrypt(b.Bytes())

	Ztdata := &mm.ZTData{
		Version:   []byte("00000007"),
		Encrypted: proto.Uint32(1),
		Data:      encData,
		TimeStamp: &curtime,
		Optype:    proto.Uint32(5),
		Uin:       proto.Uint32(0),
	}
	return Ztdata
}

type GetClientCheckDataProtoBufferRequest struct {
	BaseRequest struct {
		Op                 string `json:"op"`
		WxDat              string `json:"wxDat"`
		HostUrl            string `json:"hostUrl"`
		Psk                string `json:"psk"`
		PskAccessExtendKey string `json:"pskAccessExtendKey"`
	} `json:"baseRequest"`
	ClientVer int    `json:"clientVer"`
	Imei      string `json:"imei"`
	Name      string `json:"name"`
	VendorID  string `json:"vendorID"`
	AdID      string `json:"adID"`
}

type GetClientCheckDataProtoBufferResponse struct {
	Success bool   `json:"succ"`
	Err     string `json:"err"`
	Data    []byte `json:"data"`
}

// 24算法
func GetiPadNewSpamData(Deviceid, DeviceName string, DeviceToken mm.TrustResponse) []byte {
	T := uint32(time.Now().Unix())
	timeStamp := int(T)
	xorKey := uint8((timeStamp * 0xffffffed) + 7)

	uuid1, uuid2 := IOSUuid(Deviceid)

	if len(Deviceid) < 32 {
		Dlen := 32 - len(Deviceid)
		Fill := "ff95DODUJ4EysYiogKZSmajWCUKUg9RX"
		Deviceid = Deviceid + Fill[:Dlen]
	}

	spamDataBody := &mm.SpamDataBody{
		UnKnown1:              proto.Int32(1),
		TimeStamp:             proto.Int32(int32(timeStamp)),
		KeyHash:               proto.Int32(int32(MakeKeyHash(int(xorKey)))),
		Yes1:                  proto.String(XorEncodeStr("yes", xorKey)),
		Yes2:                  proto.String(XorEncodeStr("yes", xorKey)),
		IosVersion:            proto.String(XorEncodeStr("13.5", xorKey)),
		DeviceType:            proto.String(XorEncodeStr("iPad", xorKey)),
		UnKnown2:              proto.Int32(6),
		IdentifierForVendor:   proto.String(XorEncodeStr(uuid1, xorKey)),
		AdvertisingIdentifier: proto.String(XorEncodeStr(uuid2, xorKey)),
		Carrier:               proto.String(XorEncodeStr("中国联通", xorKey)),
		BatteryInfo:           proto.Int32(1),
		NetworkName:           proto.String(XorEncodeStr("en0", xorKey)),
		NetType:               proto.Int32(0),
		AppBundleId:           proto.String(XorEncodeStr("com.tencent.xin", xorKey)),
		DeviceName:            proto.String(XorEncodeStr(DeviceName, xorKey)),
		UserName:              proto.String(XorEncodeStr("iPad11,3", xorKey)),
		Unknown3:              proto.Int64(77968568554095637),
		Unknown4:              proto.Int64(77968568554095617),
		Unknown5:              proto.Int32(5),
		Unknown6:              proto.Int32(4),
		Lang:                  proto.String(XorEncodeStr("zh_CN", xorKey)),
		Country:               proto.String(XorEncodeStr("CN", xorKey)),
		Unknown7:              proto.Int32(4),
		DocumentDir:           proto.String(XorEncodeStr("/var/mobile/Containers/Data/Application/94E41585-A27E-4933-AF06-5ABF7C774A6F/Documents", xorKey)),
		Unknown8:              proto.Int32(0),
		Unknown9:              proto.Int32(1),
		HeadMD5:               proto.String(XorEncodeStr("901bf05e51e2cb5585760f7e0116d0ba", xorKey)),
		AppUUID:               proto.String(XorEncodeStr(uuid1, xorKey)),
		SyslogUUID:            proto.String(""),
		Unknown10:             proto.String(""),
		Unknown11:             proto.String(""),
		AppName:               proto.String(XorEncodeStr("微信", xorKey)),
		SshPath:               proto.String(""),
		TempTest:              proto.String(""),
		DevMD5:                proto.String(""),
		DevUser:               proto.String(""),
		Unknown12:             proto.String(""),
		IsModify:              proto.Int32(0),
		ModifyMD5:             proto.String(""),
		RqtHash:               proto.Int64(288530629010980929),
		Unknown43:             proto.Uint64(1586355322),
		Unknown44:             proto.Uint64(1586355519000),
		Unknown45:             proto.Uint64(0),
		Unknown46:             proto.Uint64(288530629010980929),
		Unknown47:             proto.Uint64(1),
		Unknown48:             proto.String(XorEncodeStr(Deviceid, xorKey)),
		Unknown49:             proto.String(""),
		Unknown50:             proto.String(""),
		Unknown51:             proto.String(XorEncodeStr(DeviceToken.GetTrustResponseData().GetSoftData().GetSoftConfig(), xorKey)),
		Unknown52:             proto.Uint64(0),
		Unknown53:             proto.String(""),
		Unknown54:             proto.String(XorEncodeStr(DeviceToken.GetTrustResponseData().GetDeviceToken(), xorKey)),
	}
	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/WeChat", xorKey)),
		Filepath: proto.String(XorEncodeStr("7195B97E-**************-8BDA959283F0", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/Library/MobileSubstrate/MobileSubstrate.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("3134CFB2-F722-310E-A2C7-42AE4DC131AB", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/mars.framework/mars", xorKey)),
		Filepath: proto.String(XorEncodeStr("A87DAD8E-E356-3E1E-9925-D63EA1614A95", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/andromeda.framework/andromeda", xorKey)),
		Filepath: proto.String(XorEncodeStr("EB5B920E-3AE6-3534-9DA4-C32DF72E33BD", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/OpenSSL.framework/OpenSSL", xorKey)),
		Filepath: proto.String(XorEncodeStr("8FAE149B-602B-3B9D-A620-88EA75CE153F", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/ProtobufLite.framework/ProtobufLite", xorKey)),
		Filepath: proto.String(XorEncodeStr("6F0D3077-4301-3D8F-8579-E34902547580", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/marsbridgenetwork.framework/marsbridgenetwork", xorKey)),
		Filepath: proto.String(XorEncodeStr("CFED9A03-C881-3D50-B014-732D0A09879F", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/matrixreport.framework/matrixreport", xorKey)),
		Filepath: proto.String(XorEncodeStr("1E7F06D2-DD36-31A8-AF3B-00D62054E1F9", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftCore.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("AD0CAD3B-1B51-3327-8644-8BE1FF1F0AE9", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftDispatch.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("9FCBA8ED-D8FD-3C16-9740-5E2A31F3E959", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftFoundation.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("9702769F-1F06-3001-AB75-5AD38E1F7D66", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftObjectiveC.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("1180AC10-0A92-39DB-8497-2B6D4217B8EB", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftDarwin.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("999C2967-8A06-3CD5-82D7-D156E9440A0C", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftCoreGraphics.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("DC548EF9-00F9-3A15-B5DB-05E39D9B5C5B", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftCoreFoundation.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("25114AE1-4AE9-3DBC-B3DE-7F9F9A5B45D2", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/Library/Frameworks/CydiaSubstrate.framework/Libraries/SubstrateLoader.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("54645DC0-3212-31D8-8A02-2FD67A793278", xorKey)),
	})

	srcdata, _ := proto.Marshal(spamDataBody)
	//logs.Info("spamDataBody: " + hex.EncodeToString(srcdata))

	newClientCheckData := &mm.NewClientCheckData{
		C32Cdata:  proto.Int64(int64(crc32.ChecksumIEEE([]byte(srcdata)))),
		TimeStamp: proto.Int64(time.Now().Unix()),
		Databody:  srcdata,
	}

	ccddata, _ := proto.Marshal(newClientCheckData)
	//logs.Info("spamDataWrapper: " + hex.EncodeToString(ccddata))
	//压缩数据
	compressdata := DoZlibCompress(ccddata)
	//compressdata := AE(ccddata)

	zt := new(ZT)
	zt.Init(true)
	encData := zt.Encrypt(compressdata)

	//encData := EN03(compressdata)

	return encData
}

// 获取FilePathCrc
func GetFileInfo(deviceId string, xorKey byte, guid2 string) []*mm.FileInfo {
	return []*mm.FileInfo{
		{
			Filepath: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+GenGUId(deviceId, GetCid(0x098521236654))+"/WeChat.app/WeChat", xorKey)),
			Fileuuid: proto.String(XorEncodeStr("3A1D0388-6BDB-350C-8706-80E3D15AA7C7", xorKey)),
		},
		{
			Filepath: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+GenGUId(deviceId, GetCid(0x098521236654))+"/WeChat.app/Frameworks/mars.framework/mars", xorKey)),
			Fileuuid: proto.String(XorEncodeStr("A7DA401B-3FF6-3920-A30A-1B0FA8258202", xorKey)),
		},
		{
			Filepath: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+GenGUId(deviceId, GetCid(0x098521236654))+"/WeChat.app/Frameworks/andromeda.framework/andromeda", xorKey)),
			Fileuuid: proto.String(XorEncodeStr("10F1245A-68FD-310D-98B3-0CFD51760BDE", xorKey)),
		},
		{
			Filepath: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+GenGUId(deviceId, GetCid(0x098521236654))+"/WeChat.app/Frameworks/OpenSSL.framework/OpenSSL", xorKey)),
			Fileuuid: proto.String(XorEncodeStr("8FAE149B-602B-3B9D-A620-88EA75CE153F", xorKey)),
		},
		{
			Filepath: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+GenGUId(deviceId, GetCid(0x098521236654))+"/WeChat.app/Frameworks/ProtobufLite.framework/ProtobufLite", xorKey)),
			Fileuuid: proto.String(XorEncodeStr("05BD590C-4DF6-3EDB-8316-0C9783928DD0", xorKey)),
		},
		{
			Filepath: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+GenGUId(deviceId, GetCid(0x098521236654))+"/WeChat.app/Frameworks/marsbridgenetwork.framework/marsbridgenetwork", xorKey)),
			Fileuuid: proto.String(XorEncodeStr("CFED9A03-C881-3D50-B014-732D0A09879F", xorKey)),
		},
		{
			Filepath: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+GenGUId(deviceId, GetCid(0x098521236654))+"/WeChat.app/Frameworks/matrixreport.framework/matrixreport", xorKey)),
			Fileuuid: proto.String(XorEncodeStr("1E7F06D2-DD36-31A8-AF3B-00D62054E1F9", xorKey)),
		},
	}
}

func GenGUId(DeviceId, Cid string) string {
	Md5Data := Md5Value(DeviceId + Cid)
	return fmt.Sprintf("%x-%x-%x-%x-%x", Md5Data[0:8], Md5Data[2:6], Md5Data[3:7], Md5Data[1:5], Md5Data[20:32])
}

// Md5Value 计算字符串的MD5值
func Md5Value(str string) string {
	data := []byte(str)
	has := md5.Sum(data)
	md5str := fmt.Sprintf("%x", has)
	return md5str
}

func GetCidMd5(DeviceId, Cid string) string {
	Md5Data := MD5ToLower(DeviceId + Cid)
	return "A136" + Md5Data[5:]
}
func MD5ToLower(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

func GetCid(s int) string {
	M := inttobytes(s >> 12)
	return hex.EncodeToString(M)
}

func DeviceNumber(DeviceId string) int64 {
	ssss := []byte(Md5Value(DeviceId))
	ccc := Hex2int(&ssss) >> 8
	ddd := ccc + 60000000000000000
	if ddd > 80000000000000000 {
		ddd = ddd - (80000000000000000 - ddd)
	}
	return int64(ddd)
}

// 移位操作
func EncInt(d int64) int64 {
	a, b := int64(0), int64(0)
	for i := 0; i < 16; i++ {
		a |= ((1 << (2 * i)) & d) << (2 * i)
		b |= ((1 << (2*i + 1)) & d) << (2*i + 1)
	}
	return a | b
}
func Hex2int(hexB *[]byte) uint64 {
	var retInt uint64
	hexLen := len(*hexB)
	for k, v := range *hexB {
		retInt += b2m_map[v] * exponent(16, uint64(2*(hexLen-k-1)))
	}
	return retInt
}

func exponent(a, n uint64) uint64 {
	result := uint64(1)
	for i := n; i > 0; i >>= 1 {
		if i&1 != 0 {
			result *= a
		}
		a *= a
	}
	return result
}

var wifiPrefix = []string{"TP_", "360_", "ChinaNet-", "MERCURY_", "DL-", "VF_", "HUAW-"}

func BuildRandomWifiSsid() string {
	s := rand.NewSource(time.Now().UnixNano())
	r := rand.New(s)
	i := r.Intn(len(wifiPrefix))
	randChar := make([]byte, 6)
	for x := 0; x < 6; x++ {
		randChar[x] = byte(r.Intn(26) + 65)
	}
	return wifiPrefix[i] + string(randChar)
}

func BuildRandomMac() string {
	// 00:00:00:00:00:00
	macs := make([]string, 6)
	for i := range macs {
		macs[i] = fmt.Sprintf("%02X", rand.Intn(0xff))
	}
	return strings.Join(macs, ":")
}

func GetiPhoneNewSpamData(Deviceid, DeviceName string, DeviceToken mm.TrustResponse, T int64) []byte {
	timeStamp := int(T)
	xorKey := uint8((timeStamp * 0xffffffed) + 7)

	uuid1, uuid2 := IOSUuid(Deviceid)

	if len(Deviceid) < 32 {
		Dlen := 32 - len(Deviceid)
		Fill := "ff95DODUJ4EysYiogKZSmajWCUKUg9RX"
		Deviceid = Deviceid + Fill[:Dlen]
	}

	spamDataBody := &mm.SpamDataBody{
		UnKnown1:              proto.Int32(1),
		TimeStamp:             proto.Int32(int32(timeStamp)),
		KeyHash:               proto.Int32(int32(MakeKeyHash(int(xorKey)))),
		Yes1:                  proto.String(XorEncodeStr("yes", xorKey)),
		Yes2:                  proto.String(XorEncodeStr("yes", xorKey)),
		IosVersion:            proto.String(XorEncodeStr("13.5", xorKey)),
		DeviceType:            proto.String(XorEncodeStr("iPhone", xorKey)),
		UnKnown2:              proto.Int32(6),
		IdentifierForVendor:   proto.String(XorEncodeStr(uuid1, xorKey)),
		AdvertisingIdentifier: proto.String(XorEncodeStr(uuid2, xorKey)),
		Carrier:               proto.String(XorEncodeStr("中国移动", xorKey)),
		BatteryInfo:           proto.Int32(1),
		NetworkName:           proto.String(XorEncodeStr("en0", xorKey)),
		NetType:               proto.Int32(0),
		AppBundleId:           proto.String(XorEncodeStr("com.tencent.xin", xorKey)),
		DeviceName:            proto.String(XorEncodeStr(DeviceName, xorKey)),
		UserName:              proto.String(XorEncodeStr("iPhone9,1", xorKey)),
		Unknown3:              proto.Int64(77968568554095637),
		Unknown4:              proto.Int64(77968568554095617),
		Unknown5:              proto.Int32(5),
		Unknown6:              proto.Int32(4),
		Lang:                  proto.String(XorEncodeStr("zh", xorKey)),
		Country:               proto.String(XorEncodeStr("CN", xorKey)),
		Unknown7:              proto.Int32(4),
		DocumentDir:           proto.String(XorEncodeStr("/var/mobile/Containers/Data/Application/94E41585-A27E-4933-AF06-5ABF7C774A6F/Documents", xorKey)),
		Unknown8:              proto.Int32(0),
		Unknown9:              proto.Int32(1),
		HeadMD5:               proto.String(XorEncodeStr("901bf05e51e2cb5585760f7e0116d0ba", xorKey)),
		AppUUID:               proto.String(XorEncodeStr(uuid1, xorKey)),
		SyslogUUID:            proto.String(""),
		Unknown10:             proto.String(""),
		Unknown11:             proto.String(""),
		AppName:               proto.String(XorEncodeStr("微信", xorKey)),
		SshPath:               proto.String(""),
		TempTest:              proto.String(""),
		DevMD5:                proto.String(""),
		DevUser:               proto.String(""),
		Unknown12:             proto.String(""),
		IsModify:              proto.Int32(0),
		ModifyMD5:             proto.String(""),
		RqtHash:               proto.Int64(288530629010980929),
		Unknown43:             proto.Uint64(1586355322),
		Unknown44:             proto.Uint64(1586355519000),
		Unknown45:             proto.Uint64(0),
		Unknown46:             proto.Uint64(288530629010980929),
		Unknown47:             proto.Uint64(1),
		Unknown48:             proto.String(XorEncodeStr(Deviceid, xorKey)),
		Unknown49:             proto.String(""),
		Unknown50:             proto.String(""),
		Unknown51:             proto.String(XorEncodeStr(DeviceToken.GetTrustResponseData().GetSoftData().GetSoftConfig(), xorKey)),
		Unknown52:             proto.Uint64(0),
		Unknown53:             proto.String(""),
		Unknown54:             proto.String(XorEncodeStr(DeviceToken.GetTrustResponseData().GetDeviceToken(), xorKey)),
	}
	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/WeChat", xorKey)),
		Filepath: proto.String(XorEncodeStr("7195B97E-**************-8BDA959283F0", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/Library/MobileSubstrate/MobileSubstrate.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("3134CFB2-F722-310E-A2C7-42AE4DC131AB", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/mars.framework/mars", xorKey)),
		Filepath: proto.String(XorEncodeStr("A87DAD8E-E356-3E1E-9925-D63EA1614A95", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/andromeda.framework/andromeda", xorKey)),
		Filepath: proto.String(XorEncodeStr("EB5B920E-3AE6-3534-9DA4-C32DF72E33BD", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/OpenSSL.framework/OpenSSL", xorKey)),
		Filepath: proto.String(XorEncodeStr("8FAE149B-602B-3B9D-A620-88EA75CE153F", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/ProtobufLite.framework/ProtobufLite", xorKey)),
		Filepath: proto.String(XorEncodeStr("6F0D3077-4301-3D8F-8579-E34902547580", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/marsbridgenetwork.framework/marsbridgenetwork", xorKey)),
		Filepath: proto.String(XorEncodeStr("CFED9A03-C881-3D50-B014-732D0A09879F", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/matrixreport.framework/matrixreport", xorKey)),
		Filepath: proto.String(XorEncodeStr("1E7F06D2-DD36-31A8-AF3B-00D62054E1F9", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftCore.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("AD0CAD3B-1B51-3327-8644-8BE1FF1F0AE9", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftDispatch.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("9FCBA8ED-D8FD-3C16-9740-5E2A31F3E959", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftFoundation.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("9702769F-1F06-3001-AB75-5AD38E1F7D66", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftObjectiveC.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("1180AC10-0A92-39DB-8497-2B6D4217B8EB", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftDarwin.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("999C2967-8A06-3CD5-82D7-D156E9440A0C", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftCoreGraphics.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("DC548EF9-00F9-3A15-B5DB-05E39D9B5C5B", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/private/var/containers/Bundle/Application/2F493AE2-C0EB-4B4E-A86C-CE9BA3C0FA14/WeChat.app/Frameworks/libswiftCoreFoundation.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("25114AE1-4AE9-3DBC-B3DE-7F9F9A5B45D2", xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/Library/Frameworks/CydiaSubstrate.framework/Libraries/SubstrateLoader.dylib", xorKey)),
		Filepath: proto.String(XorEncodeStr("54645DC0-3212-31D8-8A02-2FD67A793278", xorKey)),
	})

	srcdata, _ := proto.Marshal(spamDataBody)

	newClientCheckData := &mm.NewClientCheckData{
		C32Cdata:  proto.Int64(int64(crc32.ChecksumIEEE(srcdata))),
		TimeStamp: proto.Int64(T),
		Databody:  srcdata,
	}

	ccddata, _ := proto.Marshal(newClientCheckData)

	//压缩数据
	compressdata := DoZlibCompress(ccddata)

	// 使用07加密
	encData := SaeEncrypt07(compressdata)

	return encData
}

var b2m_map map[byte]uint64 = map[byte]uint64{
	0x00: 0,
	0x01: 1,
	0x02: 2,
	0x03: 3,
	0x04: 4,
	0x05: 5,
	0x06: 6,
	0x07: 7,
	0x08: 8,
	0x09: 9,
	0x0A: 10,
	0x0B: 11,
	0x0C: 12,
	0x0D: 13,
	0x0E: 14,
	0x0F: 15,
	0x10: 16,
	0x11: 17,
	0x12: 18,
	0x13: 19,
	0x14: 20,
	0x15: 21,
	0x16: 22,
	0x17: 23,
	0x18: 24,
	0x19: 25,
	0x1A: 26,
	0x1B: 27,
	0x1C: 28,
	0x1D: 29,
	0x1E: 30,
	0x1F: 31,
	0x20: 32,
	0x21: 33,
	0x22: 34,
	0x23: 35,
	0x24: 36,
	0x25: 37,
	0x26: 38,
	0x27: 39,
	0x28: 40,
	0x29: 41,
	0x2A: 42,
	0x2B: 43,
	0x2C: 44,
	0x2D: 45,
	0x2E: 46,
	0x2F: 47,
	0x30: 48,
	0x31: 49,
	0x32: 50,
	0x33: 51,
	0x34: 52,
	0x35: 53,
	0x36: 54,
	0x37: 55,
	0x38: 56,
	0x39: 57,
	0x3A: 58,
	0x3B: 59,
	0x3C: 60,
	0x3D: 61,
	0x3E: 62,
	0x3F: 63,
	0x40: 64,
	0x41: 65,
	0x42: 66,
	0x43: 67,
	0x44: 68,
	0x45: 69,
	0x46: 70,
	0x47: 71,
	0x48: 72,
	0x49: 73,
	0x4A: 74,
	0x4B: 75,
	0x4C: 76,
	0x4D: 77,
	0x4E: 78,
	0x4F: 79,
	0x50: 80,
	0x51: 81,
	0x52: 82,
	0x53: 83,
	0x54: 84,
	0x55: 85,
	0x56: 86,
	0x57: 87,
	0x58: 88,
	0x59: 89,
	0x5A: 90,
	0x5B: 91,
	0x5C: 92,
	0x5D: 93,
	0x5E: 94,
	0x5F: 95,
	0x60: 96,
	0x61: 97,
	0x62: 98,
	0x63: 99,
	0x64: 100,
	0x65: 101,
	0x66: 102,
	0x67: 103,
	0x68: 104,
	0x69: 105,
	0x6A: 106,
	0x6B: 107,
	0x6C: 108,
	0x6D: 109,
	0x6E: 110,
	0x6F: 111,
	0x70: 112,
	0x71: 113,
	0x72: 114,
	0x73: 115,
	0x74: 116,
	0x75: 117,
	0x76: 118,
	0x77: 119,
	0x78: 120,
	0x79: 121,
	0x7A: 122,
	0x7B: 123,
	0x7C: 124,
	0x7D: 125,
	0x7E: 126,
	0x7F: 127,
	0x80: 128,
	0x81: 129,
	0x82: 130,
	0x83: 131,
	0x84: 132,
	0x85: 133,
	0x86: 134,
	0x87: 135,
	0x88: 136,
	0x89: 137,
	0x8A: 138,
	0x8B: 139,
	0x8C: 140,
	0x8D: 141,
	0x8E: 142,
	0x8F: 143,
	0x90: 144,
	0x91: 145,
	0x92: 146,
	0x93: 147,
	0x94: 148,
	0x95: 149,
	0x96: 150,
	0x97: 151,
	0x98: 152,
	0x99: 153,
	0x9A: 154,
	0x9B: 155,
	0x9C: 156,
	0x9D: 157,
	0x9E: 158,
	0x9F: 159,
	0xA0: 160,
	0xA1: 161,
	0xA2: 162,
	0xA3: 163,
	0xA4: 164,
	0xA5: 165,
	0xA6: 166,
	0xA7: 167,
	0xA8: 168,
	0xA9: 169,
	0xAA: 170,
	0xAB: 171,
	0xAC: 172,
	0xAD: 173,
	0xAE: 174,
	0xAF: 175,
	0xB0: 176,
	0xB1: 177,
	0xB2: 178,
	0xB3: 179,
	0xB4: 180,
	0xB5: 181,
	0xB6: 182,
	0xB7: 183,
	0xB8: 184,
	0xB9: 185,
	0xBA: 186,
	0xBB: 187,
	0xBC: 188,
	0xBD: 189,
	0xBE: 190,
	0xBF: 191,
	0xC0: 192,
	0xC1: 193,
	0xC2: 194,
	0xC3: 195,
	0xC4: 196,
	0xC5: 197,
	0xC6: 198,
	0xC7: 199,
	0xC8: 200,
	0xC9: 201,
	0xCA: 202,
	0xCB: 203,
	0xCC: 204,
	0xCD: 205,
	0xCE: 206,
	0xCF: 207,
	0xD0: 208,
	0xD1: 209,
	0xD2: 210,
	0xD3: 211,
	0xD4: 212,
	0xD5: 213,
	0xD6: 214,
	0xD7: 215,
	0xD8: 216,
	0xD9: 217,
	0xDA: 218,
	0xDB: 219,
	0xDC: 220,
	0xDD: 221,
	0xDE: 222,
	0xDF: 223,
	0xE0: 224,
	0xE1: 225,
	0xE2: 226,
	0xE3: 227,
	0xE4: 228,
	0xE5: 229,
	0xE6: 230,
	0xE7: 231,
	0xE8: 232,
	0xE9: 233,
	0xEA: 234,
	0xEB: 235,
	0xEC: 236,
	0xED: 237,
	0xEE: 238,
	0xEF: 239,
	0xF0: 240,
	0xF1: 241,
	0xF2: 242,
	0xF3: 243,
	0xF4: 244,
	0xF5: 245,
	0xF6: 246,
	0xF7: 247,
	0xF8: 248,
	0xF9: 249,
	0xFA: 250,
	0xFB: 251,
	0xFC: 252,
	0xFD: 253,
	0xFE: 254,
	0xFF: 255,
}

// RqtKey 计算RQT用到到Key
var RqtKey = []byte{0x6a, 0x66, 0x4d, 0x5d, 0x53, 0x7c, 0x25, 0x3f, 0x73, 0x6e, 0x48, 0x27, 0x3a, 0x29, 0x5e, 0x4f}

// SignRqtBufByAutoChosenKey 计算RQT值
func SignRqtBufByAutoChosenKey(md5Value string) uint32 {
	// tmpRqtKeyOne
	rqtLen := len(RqtKey)
	totalLength := 48 + rqtLen
	blockOne := make([]byte, totalLength)
	for index := 0; index < totalLength; index++ {
		blockOne[index] = 0
		if index < rqtLen {
			blockOne[index] = RqtKey[index]
		}
		blockOne[index] = blockOne[index] ^ 0x36
	}

	// tmpRqtKeyTwo
	blockTwo := make([]byte, totalLength)
	for index := 0; index < totalLength; index++ {
		blockTwo[index] = 0
		if index < rqtLen {
			blockTwo[index] = RqtKey[index]
		}
		blockTwo[index] = blockTwo[index] ^ 0x5c
	}
	// 计算Hash值
	tmpData := append(blockOne, []byte(md5Value)[0:]...)
	shaValue := Sha1(tmpData)
	tmpData = append(blockTwo, shaValue[0:]...)
	shaValue = Sha1(tmpData)

	// 计算key1，key2, key3
	key1 := 0
	key2 := 0
	key3 := 0
	tmpLen := len(shaValue) - 2
	for index := 0; index < tmpLen; index++ {
		tmpValue1 := shaValue[index] & 0xff
		tmpValue2 := shaValue[index+1] & 0xff
		tmpValue3 := shaValue[index+2] & 0xff

		key1 = key1*0x83 + int(tmpValue1)
		key2 = key2*0x83 + int(tmpValue2)
		key3 = key3*0x83 + int(tmpValue3)
	}

	// 计算RQT值
	key1 = key1 & 0x7f
	key2 = (key2 << 8) & 0x7f00
	key3 = (key3 << 16) & 0x7f0000
	retValue := key1 | key3 | key2
	retValue = retValue | 0x21000000
	return uint32(retValue)
}

// Sha1 计算Sha值
func Sha1(data []byte) []byte {
	sha1 := sha1.New()
	sha1.Write(data)
	return sha1.Sum([]byte(""))
}

// 注释掉与CCD相关的函数
/*
// UpdateJointAngle 使用CCD(07算法)更新关节角度
func (c *CCD) UpdateJointAngle(targetPosition mgl32.Vec3) {
	const maxIterations = 10
	const epsilon = 0.001

	for iteration := 0; iteration < maxIterations; iteration++ {
		// 从末端关节向基座方向迭代
		for i := len(c.joints) - 1; i >= 0; i-- {
			endEffector := c.GetEndEffectorPosition()

			// 如果末端执行器已经足够接近目标，则退出
			if endEffector.Sub(targetPosition).Len() < epsilon {
				return
			}

			currentJoint := c.joints[i]
			jointPos := currentJoint.GetPosition()

			// 计算向量
			toEndEffector := endEffector.Sub(jointPos)
			toTarget := targetPosition.Sub(jointPos)

			// 归一化向量
			toEndEffector = toEndEffector.Normalize()
			toTarget = toTarget.Normalize()

			// 计算旋转轴和角度
			rotationAxis := toEndEffector.Cross(toTarget)
			if rotationAxis.Len() < epsilon {
				continue
			}
			rotationAxis = rotationAxis.Normalize()

			// 计算旋转角度
			cosTheta := toEndEffector.Dot(toTarget)
			theta := float32(math.Acos(float64(cosTheta)))

			// 限制旋转角度
			maxRotation := float32(0.1) // 每次迭代的最大旋转角度
			if theta > maxRotation {
				theta = maxRotation
			}

			// 应用旋转
			rotation := mgl32.QuatRotate(theta, rotationAxis)
			currentJoint.UpdateRotation(rotation)
		}
	}
}
*/

// IphoneWcstf07 生成wcstf, 使用07加密
func IphoneWcstf07(Username string) []byte {
	curtime := uint64(time.Now().UnixNano() / 1e6)
	contentlen := len(Username)

	var ct []uint64
	ut := curtime
	for i := 0; i < contentlen; i++ {
		ut += uint64(rand.Intn(10000))
		ct = append(ct, ut)
	}
	ccd := &mm.Wcstf{
		StartTime: &curtime,
		CheckTime: &curtime,
		Count:     proto.Uint32(uint32(contentlen)),
		EndTime:   ct,
	}

	pb, _ := proto.Marshal(ccd)

	compressData := DoZlibCompress(pb)
	encData := SaeEncrypt07(compressData)

	Ztdata := &mm.ZTData{
		Version:   []byte("00000007"),
		Encrypted: proto.Uint32(1),
		Data:      encData,
		TimeStamp: proto.Uint32(uint32(time.Now().Unix())),
		Optype:    proto.Uint32(5),
		Uin:       proto.Uint32(0),
	}

	MS, _ := proto.Marshal(Ztdata)
	return MS
}

// IphoneWcste07 生成wcste, 使用07加密
func IphoneWcste07(A, B uint64) []byte {
	curtime := uint32(time.Now().Unix())
	curNanoTime := uint64(time.Now().UnixNano())

	ccd := &mm.Wcste{
		Checkid:   proto.String("<LoginByID>"),
		StartTime: &curtime,
		CheckTime: &curtime,
		Count1:    proto.Uint32(0),
		Count2:    proto.Uint32(1),
		Count3:    proto.Uint32(0),
		Const1:    proto.Uint64(A),
		Const2:    &curNanoTime,
		Const3:    &curNanoTime,
		Const4:    &curNanoTime,
		Const5:    &curNanoTime,
		Const6:    proto.Uint64(B),
	}

	pb, _ := proto.Marshal(ccd)

	compressData := DoZlibCompress(pb)
	encData := SaeEncrypt07(compressData)

	Ztdata := &mm.ZTData{
		Version:   []byte("00000007"),
		Encrypted: proto.Uint32(1),
		Data:      encData,
		TimeStamp: proto.Uint32(uint32(time.Now().Unix())),
		Optype:    proto.Uint32(5),
		Uin:       proto.Uint32(0),
	}

	MS, _ := proto.Marshal(Ztdata)
	return MS
}

// GetiPhoneNewSpamData07 使用07算法加密的iPhone设备数据生成
func GetiPhoneNewSpamData07(Deviceid, DeviceName string, DeviceToken mm.TrustResponse) []byte {
	timeStamp := int(time.Now().Unix())
	xorKey := uint8((timeStamp * 0xffffffed) + 7)

	uuid1, uuid2 := IOSUuid(Deviceid)

	if len(Deviceid) < 32 {
		Dlen := 32 - len(Deviceid)
		Fill := "ff95DODUJ4EysYiogKZSmajWCUKUg9RX"
		Deviceid = Deviceid + Fill[:Dlen]
	}

	spamDataBody := &mm.SpamDataBody{
		UnKnown1:              proto.Int32(1),
		TimeStamp:             proto.Int32(int32(timeStamp)),
		KeyHash:               proto.Int32(int32(MakeKeyHash(int(xorKey)))),
		Yes1:                  proto.String(XorEncodeStr("yes", xorKey)),
		Yes2:                  proto.String(XorEncodeStr("yes", xorKey)),
		IosVersion:            proto.String(XorEncodeStr("13.5", xorKey)),
		DeviceType:            proto.String(XorEncodeStr("iPhone", xorKey)),
		UnKnown2:              proto.Int32(2),
		IdentifierForVendor:   proto.String(XorEncodeStr(uuid1, xorKey)),
		AdvertisingIdentifier: proto.String(XorEncodeStr(uuid2, xorKey)),
		Carrier:               proto.String(XorEncodeStr("中国移动", xorKey)),
		BatteryInfo:           proto.Int32(1),
		NetworkName:           proto.String(XorEncodeStr("en0", xorKey)),
		NetType:               proto.Int32(1),
		AppBundleId:           proto.String(XorEncodeStr("com.tencent.xin", xorKey)),
		DeviceName:            proto.String(XorEncodeStr(DeviceName, xorKey)),
		UserName:              proto.String(XorEncodeStr("iPhone9,1", xorKey)),
		Unknown3:              proto.Int64(IOSDeviceNumber(Deviceid[:29] + "FFF")),
		Unknown4:              proto.Int64(IOSDeviceNumber(Deviceid[:29] + "OOO")),
		Unknown5:              proto.Int32(1),
		Unknown6:              proto.Int32(4),
		Lang:                  proto.String(XorEncodeStr("zh", xorKey)),
		Country:               proto.String(XorEncodeStr("CN", xorKey)),
		Unknown7:              proto.Int32(4),
		DocumentDir:           proto.String(XorEncodeStr("/var/mobile/Containers/Data/Application/"+IOSGetCidUUid(Deviceid, IOSGetCid(0x10101201))+"/Documents", xorKey)),
		Unknown8:              proto.Int32(0),
		Unknown9:              proto.Int32(0),
		HeadMD5:               proto.String(XorEncodeStr(IOSGetCidMd5(Deviceid, IOSGetCid(0x0262626262626)), xorKey)),
		AppUUID:               proto.String(XorEncodeStr(uuid1, xorKey)),
		SyslogUUID:            proto.String(XorEncodeStr(uuid2, xorKey)),
		Unknown10:             proto.String(""),
		Unknown11:             proto.String(""),
		AppName:               proto.String(XorEncodeStr("微信", xorKey)),
		SshPath:               proto.String(""),
		TempTest:              proto.String(""),
		DevMD5:                proto.String(""),
		DevUser:               proto.String(""),
		Unknown12:             proto.String(""),
		IsModify:              proto.Int32(0),
		ModifyMD5:             proto.String(""),
		RqtHash:               proto.Int64(288529533794259264),
		Unknown43:             proto.Uint64(1586355322),
		Unknown44:             proto.Uint64(1586355519000),
		Unknown45:             proto.Uint64(0),
		Unknown46:             proto.Uint64(288529533794259264),
		Unknown47:             proto.Uint64(0),
		Unknown48:             proto.String(Deviceid),
		Unknown49:             proto.String(""),
		Unknown50:             proto.String(""),
		Unknown51:             proto.String(XorEncodeStr(DeviceToken.GetTrustResponseData().GetSoftData().GetSoftConfig(), xorKey)),
		Unknown52:             proto.Uint64(0),
		Unknown53:             proto.String(""),
		Unknown54:             proto.String(XorEncodeStr(DeviceToken.GetTrustResponseData().GetDeviceToken(), xorKey)),
	}
	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+IOSGetCidUUid(Deviceid, IOSGetCid(0x098521236654))+"/WeChat.app/WeChat", xorKey)),
		Filepath: proto.String(XorEncodeStr(IOSGetCidUUid(Deviceid, IOSGetCid(0x30000001)), xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+IOSGetCidUUid(Deviceid, IOSGetCid(0x098521236654))+"/WeChat.app/Frameworks/OpenSSL.framework/OpenSSL", xorKey)),
		Filepath: proto.String(XorEncodeStr(IOSGetCidUUid(Deviceid, IOSGetCid(0x30000002)), xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+IOSGetCidUUid(Deviceid, IOSGetCid(0x098521236654))+"/WeChat.app/Frameworks/ProtobufLite.framework/ProtobufLite", xorKey)),
		Filepath: proto.String(XorEncodeStr(IOSGetCidUUid(Deviceid, IOSGetCid(0x30000003)), xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+IOSGetCidUUid(Deviceid, IOSGetCid(0x098521236654))+"/WeChat.app/Frameworks/marsbridgenetwork.framework/marsbridgenetwork", xorKey)),
		Filepath: proto.String(XorEncodeStr(IOSGetCidUUid(Deviceid, IOSGetCid(0x30000004)), xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+IOSGetCidUUid(Deviceid, IOSGetCid(0x098521236654))+"/WeChat.app/Frameworks/matrixreport.framework/matrixreport", xorKey)),
		Filepath: proto.String(XorEncodeStr(IOSGetCidUUid(Deviceid, IOSGetCid(0x30000005)), xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+IOSGetCidUUid(Deviceid, IOSGetCid(0x098521236654))+"/WeChat.app/Frameworks/andromeda.framework/andromeda", xorKey)),
		Filepath: proto.String(XorEncodeStr(IOSGetCidUUid(Deviceid, IOSGetCid(0x30000006)), xorKey)),
	})

	spamDataBody.AppFileInfo = append(spamDataBody.AppFileInfo, &mm.FileInfo{
		Fileuuid: proto.String(XorEncodeStr("/var/containers/Bundle/Application/"+IOSGetCidUUid(Deviceid, IOSGetCid(0x098521236654))+"/WeChat.app/Frameworks/mars.framework/mars", xorKey)),
		Filepath: proto.String(XorEncodeStr(IOSGetCidUUid(Deviceid, IOSGetCid(0x30000007)), xorKey)),
	})
	srcdata, _ := proto.Marshal(spamDataBody)

	newClientCheckData := &mm.NewClientCheckData{
		C32Cdata:  proto.Int64(int64(crc32.ChecksumIEEE(srcdata))),
		TimeStamp: proto.Int64(int64(timeStamp)),
		Databody:  srcdata,
	}

	ccddata, _ := proto.Marshal(newClientCheckData)

	//压缩数据
	compressdata := DoZlibCompress(ccddata)

	// 使用07加密
	encData := SaeEncrypt07(compressdata)

	return encData
}
