package Algorithm

import (
	"fmt"
	"wechatdll/Cilent/mm"

	"github.com/golang/protobuf/proto"
)

func GetIOSExtSpamInfoAndDeviceToken(Wxid, Deviceid_str, DeviceName string, DeviceToken mm.TrustResponse, T int64) []byte {
	ccData := &mm.CryptoData{
		Version:     []byte("00000007"),
		Type:        proto.Uint32(1),
		EncryptData: GetiPhoneNewSpamData07(Deviceid_str, DeviceName, DeviceToken),
		Timestamp:   proto.Uint32(uint32(T)),
		Unknown5:    proto.Uint32(5),
		Unknown6:    proto.Uint32(0),
	}
	ccDataseq, _ := proto.Marshal(ccData)
	fmt.Printf("mm:%+v\n", DeviceToken)
	fmt.Println(DeviceToken.GetTrustResponseData().GetDeviceToken())
	DeviceTokenCCD := &mm.DeviceToken{
		Version:   proto.String(""),
		Encrypted: proto.Uint32(1),
		Data: &mm.SKBuiltinStringT{
			String_: proto.String(DeviceToken.GetTrustResponseData().GetDeviceToken()),
		},
		TimeStamp: proto.Uint32(uint32(T)),
		Optype:    proto.Uint32(2),
		Uin:       proto.Uint32(0),
	}
	DeviceTokenCCDPB, _ := proto.Marshal(DeviceTokenCCD)

	Wcstf := IphoneWcstf07(Wxid)
	Wcste := IphoneWcste07(0, 0)

	WCExtInfo := &mm.WCExtInfo{
		Wcstf: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Wcstf))),
			Buffer: Wcstf,
		},
		Wcste: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(Wcste))),
			Buffer: Wcste,
		},
		CcData: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(ccDataseq))),
			Buffer: ccDataseq,
		},
		DeviceToken: &mm.SKBuiltinBufferT{
			ILen:   proto.Uint32(uint32(len(DeviceTokenCCDPB))),
			Buffer: DeviceTokenCCDPB,
		},
	}

	WCExtInfoseq, _ := proto.Marshal(WCExtInfo)
	fmt.Println(WCExtInfoseq)

	return WCExtInfoseq
}
