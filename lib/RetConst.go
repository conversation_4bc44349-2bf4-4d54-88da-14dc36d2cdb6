package lib

import (
	"encoding/binary"
	"wechatdll/Cilent/mm"
)

func RetConst(data []byte) (int64, string) {
	var Ret int32
	Ret = BytesToInt32(data[2:10])
	return int64(Ret), mm.RetConst_name[BytesToInt32(data[2:10])]
}

func BytesToInt32(buf []byte) int32 {
	return int32(binary.BigEndian.Uint32(buf))
}

func Int32ToBytes(i int32) []byte {
	buf := make([]byte, 8)
	binary.BigEndian.PutUint32(buf, uint32(i))
	return buf
}
