package comm

import (
	"github.com/robfig/cron/v3"
	"time"
)

func Error(error string) map[string]interface{} {
	m1 := make(map[string]interface{})
	m1["Code"] = -1
	m1["Success"] = false
	m1["Message"] = error
	return m1
}

func MsgStartup(msg string, TaskID cron.EntryID) map[string]interface{} {
	m1 := make(map[string]interface{})
	m1["Code"] = 0
	m1["Success"] = true
	m1["Message"] = msg
	m1["TaskID"] = TaskID
	return m1
}

func TaskMsgCheck(msg string, CheckID cron.EntryID, Next time.Time, Prev time.Time) map[string]interface{} {
	m1 := make(map[string]interface{})
	m1["Code"] = 0
	m1["Success"] = true
	m1["Message"] = msg
	m1["CheckID"] = CheckID
	m1["Next"] = Next.Format("2006-01-02 15:04:05")
	m1["Prev"] = Prev.Format("2006-01-02 15:04:05")
	return m1

}

func TaskMsgClose(msg string, TaskID cron.EntryID) map[string]interface{} {
	m1 := make(map[string]interface{})
	m1["Code"] = 0
	m1["Success"] = true
	m1["Message"] = msg
	m1["TaskID"] = TaskID
	return m1
}
