package comm

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/astaxie/beego"
	"github.com/go-redis/redis"
	"github.com/robfig/cron/v3"
	"time"
	"wechatdll/Cilent/mm"
	"wechatdll/Mmtls"
	"wechatdll/models"
)

var RedisClient *redis.Client

type LoginData struct {
	Uin                        uint32
	Wxid                       string
	Pwd                        string
	Uuid                       string
	Aeskey                     []byte
	NotifyKey                  []byte
	Deviceid_str               string
	Deviceid_byte              []byte
	DeviceType                 string
	ClientVersion              int
	DeviceName                 string
	NickName                   string
	Alais                      string
	Mobile                     string
	MmtlsHost                  string
	Sessionkey                 []byte
	Sessionkey_2               []byte
	Autoauthkey                []byte
	Autoauthkeylen             int32
	Clientsessionkey           []byte
	Serversessionkey           []byte
	HybridEcdhPrivkey          []byte
	HybridEcdhPubkey           []byte
	HybridEcdhInitServerPubKey []byte
	Loginecdhkey               []byte
	Cooike                     []byte
	LoginMode                  string
	Proxy                      models.ProxyInfo
	MmtlsKey                   *Mmtls.MmtlsClient
	DeviceToken                mm.TrustResponse
	Task                       Task
	Dns                        []Dns
	Key                        key
	PushUrl                    string
	HeadUrl                    string
	Email                      string
	SyncKey                    []byte
}

type DeviceTokenKey struct {
}

type Task struct {
	MsgSync cron.EntryID
}

type Dns struct {
	Ip   string
	Host string
}

type key struct {
	MsgSyncKEY string
}

func SetTodayMoney(key string, fieldKey string, data float64, dataType int) error {
	prefixStr := ""
	switch dataType {
	case 1:
		{
			prefixStr = "wxhb:"
			break
		}
	case 2:
		{
			prefixStr = "wxzz:"
			break
		}

	}
	moneyKey := prefixStr + key
	// 首先获取今天的金额
	todayMoney, _ := RedisClient.HGet(moneyKey, fieldKey).Float64()
	totalMoney := todayMoney + data
	err := RedisClient.HSet(moneyKey, fieldKey, totalMoney).Err()
	if err != nil {
		return err
	}
	return nil
}

func RedisInitialize() *redis.Client {
	DB, err := beego.AppConfig.Int("redisdb")
	redishost := beego.AppConfig.String("redishost")
	redispwd := beego.AppConfig.String("redispwd")
	poolSize, err := beego.AppConfig.Int("poolSize")
	minIdleConns, err := beego.AppConfig.Int("minIdleConns")
	if err != nil {
		DB = 0
	}

	RedisClient = redis.NewClient(&redis.Options{
		//Addr:     "r-uf6kp7cjksr1boe1g2.redis.rds.aliyuncs.com:6379", // redis地址
		Addr:         redishost,
		Password:     redispwd, // redis密码，没有则留空
		DB:           DB,       // 默认数据库，默认是0
		PoolSize:     poolSize,
		MinIdleConns: minIdleConns,
	})

	return RedisClient
}

func CreateLoginData(data LoginData, key string, Expiration int64) error {
	var ExpTime time.Duration
	if key == "" {
		key = data.Uuid
	}

	if Expiration > 0 {
		ExpTime = time.Second * time.Duration(Expiration)
	} else {
		ExpTime = 0
	}

	JsonData, _ := json.Marshal(&data)
	err := RedisClient.Set(key, string(JsonData), ExpTime).Err()
	if err != nil {
		return err
	}
	return nil
}

func GetKeyJsonData(Key string) (ret string, err error) {
	val, _ := RedisClient.Get(Key).Result()
	if val == "" {
		return ret, errors.New(fmt.Sprintf("[Key:%v]数据不存在", Key))
	}
	return val, nil
}

func GetLoginata(key string) (*LoginData, error) {
	P, err := GetKeyJsonData(key)
	if err != nil {
		return &LoginData{}, err
	}
	D := &LoginData{}
	err = json.Unmarshal([]byte(P), D)
	if err != nil {
		return &LoginData{}, err
	}

	return D, nil
}

func DelLoginata(key string) error {
	return RedisClient.Del(key).Err()
}
